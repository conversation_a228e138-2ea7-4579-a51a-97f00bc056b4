/* eslint-disable */
export const departureReturnResult = {
  success: true,
  error: {
    code: '',
    message: ''
  },
  result: {
    itinerary_id: 718,
    extra_id: 99914,
    extra_type: 'SPU',
    tour_days: 2,
    tour_type: 'multi_day',
    days: [
      {
        instances: [
          {
            instance_basic_id: 23,
            data_index: 0,
            instance_index: 0,
            itinerary_type: 'departure',
            instance_custom_infos: [
              {
                custom_instance_id: 17,
                custom_instance_type: 'departure_custom',
                custom_instance_json: {
                  departure_reminder_list: [
                    {
                      value: 1
                    },
                    {
                      value: 2
                    },
                    {
                      data: {
                        time: 3
                      },
                      value: 3
                    }
                  ],
                  can_sync_api_info: true,
                  api_sync_status: 2,
                  edit_type: 2,
                  sync_supplier_name: '',
                  departure_type: 3,
                  time_select: [
                    {
                      h: '08',
                      m: '08'
                    },
                    {
                      h: '08',
                      hours_num: 8,
                      m: '09',
                      mins_num: 9
                    },
                    {
                      h: '11',
                      hours_num: 8,
                      m: '12',
                      mins_num: 10
                    }
                  ]
                },
                attr_controls: [
                  {
                    key: 'sync_supplier_name',
                    control: {
                      can_edit: 1,
                      can_view: 1
                    }
                  },
                  {
                    key: 'edit_type',
                    control: {
                      can_edit: 1,
                      can_view: 1
                    }
                  }
                  // {
                  //   key: 'time_select', // todo
                  //   control: {
                  //     can_edit: 1,
                  //     can_view: 1
                  //   },
                  //   extra_info: {
                  //     disable_sku_timeslot_list: ['07:55']
                  //   }
                  // }
                ]
              }
            ],
            instance_detail_infos: [
              {
                detail_instance_id: 33,
                detail_instance_type: 'departure_pick_up_detail',
                detail_instance_index: 0,
                modules: [
                  {
                    module_id: 33,
                    module_type: 'image',
                    data: {
                      images: []
                    },
                    ref_language_data: null
                  },
                  {
                    module_id: 26,
                    module_type: 'language',
                    data: {
                      name: ''
                    },
                    ref_language_data: {
                      name: ''
                    }
                  },
                  {
                    module_id: 33,
                    module_type: 'departure_poi',
                    data: {
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: [
                          {
                            address_desc: '',
                            administrative_level: 2,
                            amap_code: '',
                            area_id: 23301,
                            customized_config: {
                              customized_area_name: '',
                              extra_fee: {
                                price_currency: 'CNY',
                                price_value: '123'
                              },
                              is_selected: 1
                            },
                            google_place_id: 'ChIJkVLh0Aj0AzQRyYCStw1V7v0',
                            id: 0,
                            location: '22.543096,114.057865',
                            location_name: 'Shenzhen City',
                            location_original: '',
                            map_type: 1,
                            parent_name: 'Guangdong',
                            path: [364890, 2001, 1020, 105],
                            pick_up_type: 2,
                            supply_api_mapping_key: ''
                          }
                        ],
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: '',
                        is_customized_area: 0,
                        location: '',
                        location_original: '',
                        name: '',
                        place_id: '',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: '',
                        type: 0,
                        version: 'v3'
                      },
                      time_select_from_to: [
                        {
                          from: {
                            h: '23',
                            m: '09'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '18',
                            m: '01'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '19',
                            m: '02'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ]
                    },
                    ref_language_data: {
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: [
                          {
                            address_desc: '',
                            administrative_level: 2,
                            amap_code: '',
                            area_id: 23301,
                            customized_config: {
                              customized_area_name: '',
                              extra_fee: {
                                price_currency: 'CNY',
                                price_value: '123'
                              },
                              is_selected: 1
                            },
                            google_place_id: 'ChIJkVLh0Aj0AzQRyYCStw1V7v0',
                            id: 0,
                            location: '22.543096,114.057865',
                            location_name: 'Shenzhen City',
                            location_original: '',
                            map_type: 1,
                            parent_name: 'Guangdong',
                            path: [364890, 2001, 1020, 105],
                            pick_up_type: 2,
                            supply_api_mapping_key: ''
                          }
                        ],
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: '',
                        is_customized_area: 0,
                        location: '',
                        location_original: '',
                        name: '',
                        place_id: '',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: '',
                        type: 0,
                        version: 'v3'
                      },
                      time_select_from_to: [
                        {
                          from: {
                            h: '23',
                            m: '09'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '18',
                            m: '01'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '19',
                            m: '02'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ]
                    }
                  }
                ]
              },
              {
                detail_instance_id: 34,
                detail_instance_type: 'departure_meet_up_detail',
                detail_instance_index: 1,
                attr_controls: [
                  {
                    key: 'image',
                    control: {
                      can_edit: 1,
                      can_view: 1
                    },
                    extra_info: null
                  },
                  {
                    key: 'language',
                    control: {
                      can_edit: 1,
                      can_view: 1
                    },
                    extra_info: null
                  },
                  {
                    key: 'departure_poi',
                    control: {
                      can_edit: 1,
                      can_view: 1
                    },
                    extra_info: {
                      // disable_list: ['title_multilang', 'custom_title_multilang', 'supply_api_mapping_key', 'time_select_from_to']
                    }
                  } // eztest controls
                ],
                modules: [
                  {
                    module_id: 34,
                    module_type: 'image',
                    data: {
                      images: []
                    },
                    ref_language_data: null
                  },
                  {
                    module_id: 27,
                    module_type: 'language',
                    data: {
                      name: ''
                    },
                    ref_language_data: {
                      name: ''
                    }
                  },
                  {
                    module_id: 138,
                    module_type: 'departure_poi',
                    data: {
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: [],
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: 'ez',
                        is_customized_area: 0,
                        location: '51.61873130344353,-0.019051444457998112',
                        location_original: '',
                        name: '',
                        place_id: 'ChIJk62ZwBUedkgRM-xHVeINY9E',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: '10 Hurst Ave',
                        type: 0,
                        version: 'v3'
                      },
                      time_select_from_to: [
                        {
                          from: {
                            h: '22',
                            m: '16'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '19',
                            m: '01'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '19',
                            m: '02'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ]
                    },
                    ref_language_data: {
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: [],
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: 'ez',
                        is_customized_area: 0,
                        location: '51.61873130344353,-0.019051444457998112',
                        location_original: '',
                        name: '',
                        place_id: 'ChIJk62ZwBUedkgRM-xHVeINY9E',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: '10 Hurst Ave',
                        type: 0,
                        version: 'v3'
                      },
                      time_select_from_to: [
                        {
                          from: {
                            h: '22',
                            m: '16'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '19',
                            m: '01'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '19',
                            m: '02'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ]
                    }
                  }
                ]
              },
              {
                detail_instance_id: 35,
                detail_instance_type: 'departure_meet_up_detail',
                detail_instance_index: 2,
                attr_controls: null,
                modules: [
                  {
                    module_id: 35,
                    module_type: 'image',
                    data: {
                      images: []
                    },
                    ref_language_data: null
                  },
                  {
                    module_id: 28,
                    module_type: 'language',
                    data: {
                      name: ''
                    },
                    ref_language_data: {
                      name: ''
                    }
                  },
                  {
                    module_id: 35,
                    module_type: 'departure_poi',
                    data: {
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: null,
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: "Shang Hai ShiHuang Pu QuPeople's SquareXizang Road (M)96",
                        is_customized_area: 0,
                        location: '31.232067325586893,121.47316455819701',
                        location_original: '',
                        name: '',
                        place_id: 'ChIJJSACOmhwsjURYusH6_NiZ_c',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: "Shang Hai ShiHuang Pu QuPeople's SquareXizang Road (M)96",
                        type: 0,
                        version: ''
                      },
                      time_select_from_to: [
                        {
                          from: {
                            h: '23',
                            m: '11'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '21',
                            m: '03'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '21',
                            m: '05'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ]
                    },
                    ref_language_data: {
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: null,
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: "Shang Hai ShiHuang Pu QuPeople's SquareXizang Road (M)96",
                        is_customized_area: 0,
                        location: '31.232067325586893,121.47316455819701',
                        location_original: '',
                        name: '',
                        place_id: 'ChIJJSACOmhwsjURYusH6_NiZ_c',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: "Shang Hai ShiHuang Pu QuPeople's SquareXizang Road (M)96",
                        type: 0,
                        version: ''
                      },
                      time_select_from_to: [
                        {
                          from: {
                            h: '23',
                            m: '11'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '21',
                            m: '03'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '21',
                            m: '05'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            ],
            card_info: {
              card_title: 'Departure info',
              infos: [
                {
                  text: 'Meet-up and pick-up both available * 08 : 08 ,08 : 09   \u0026 more'
                },
                {
                  text: '1 pick-up areas in Shenzhen and 2 meet-up points in London,Shanghai '
                }
              ]
            }
          }
        ]
      },
      {
        instances: [
          {
            instance_basic_id: 24,
            data_index: 1,
            instance_index: 0,
            itinerary_type: 'return',
            instance_custom_infos: [
              {
                custom_instance_id: 18,
                custom_instance_type: 'return_custom',
                custom_instance_json: {
                  departure_reminder_list: [
                    {
                      data: {
                        time: 1
                      },
                      value: 0
                    }
                  ],
                  departure_type: 0,
                  time_select: [
                    {
                      h: '',
                      m: ''
                    }
                  ]
                },
                attr_controls: null
              }
            ],
            instance_detail_infos: [
              {
                detail_instance_id: 36,
                detail_instance_type: 'return_detail',
                detail_instance_index: 0,
                attr_controls: null,
                modules: [
                  {
                    module_id: 36,
                    module_type: 'image',
                    data: {
                      images: []
                    },
                    ref_language_data: null
                  },
                  {
                    module_id: 29,
                    module_type: 'language',
                    data: {
                      name: '23456'
                    },
                    ref_language_data: {
                      name: '23456'
                    }
                  },
                  {
                    module_id: 139,
                    module_type: 'return_poi',
                    data: {
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: [],
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: '',
                        is_customized_area: 0,
                        location: '',
                        location_original: '',
                        name: '',
                        place_id: '',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: '',
                        type: 0,
                        version: 'v3'
                      },
                      return_type: 0,
                      time_select_from_to: [
                        {
                          from: {
                            h: '22',
                            m: '17'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '19',
                            m: '01'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '21',
                            m: '04'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ]
                    },
                    ref_language_data: {
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: [],
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: '',
                        is_customized_area: 0,
                        location: '',
                        location_original: '',
                        name: '',
                        place_id: '',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: '',
                        type: 0,
                        version: 'v3'
                      },
                      return_type: 0,
                      time_select_from_to: [
                        {
                          from: {
                            h: '22',
                            m: '17'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '19',
                            m: '01'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '21',
                            m: '04'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            ],
            card_info: {
              card_title: 'Return info',
              infos: [
                {
                  text: 'No, tour ends at the last stop * 22 : 17 ,19 : 01 ,21 : 04 '
                }
              ]
            }
          }
        ]
      }
    ],
    template_sample: [
      {
        instance_basic_id: 0,
        data_index: 0,
        instance_index: 0,
        itinerary_type: 'departure',
        instance_custom_infos: [
          {
            custom_instance_id: 0,
            custom_instance_type: 'departure_custom',
            custom_instance_json: {
              departure_reminder_list: [
                {
                  data: {
                    time: 1
                  },
                  value: 0
                }
              ],
              departure_type: 0,
              time_select: [
                {
                  h: '',
                  m: ''
                }
              ]
            },
            attr_controls: [
              {
                key: 'departure_type',
                control: {
                  can_edit: 0,
                  can_view: 1
                },
                extra_info: null
              }
            ]
          }
        ],
        instance_detail_infos: [
          {
            detail_instance_id: 0,
            detail_instance_type: 'departure_pick_up_detail',
            detail_instance_index: 0,
            attr_controls: [
              {
                key: 'image',
                control: {
                  can_edit: 0,
                  can_view: 1
                },
                extra_info: null
              }
            ],
            modules: [
              {
                module_id: 0,
                module_type: 'image',
                data: {
                  images: []
                },
                ref_language_data: null
              },
              {
                module_id: 0,
                module_type: 'language',
                data: {
                  name: ''
                },
                ref_language_data: null
              },
              {
                module_id: 0,
                module_type: 'departure_poi',
                data: {
                  map: {
                    address_desc: '',
                    amap_code: '',
                    area_data: [],
                    city_name: '',
                    country_name: '',
                    custom_title_multilang: '',
                    is_customized_area: 0,
                    location: '',
                    location_original: '',
                    name: '',
                    place_id: '',
                    poi_id: 0,
                    supply_api_mapping_key: '',
                    title_multilang: '',
                    type: -1,
                    version: 'v3'
                  },
                  time_select_from_to: [
                    {
                      from: {
                        h: '',
                        m: ''
                      },
                      to: {
                        h: '',
                        m: ''
                      }
                    }
                  ]
                },
                ref_language_data: null
              }
            ]
          },
          {
            detail_instance_id: 0,
            detail_instance_type: 'departure_meet_up_detail',
            detail_instance_index: 0,
            attr_controls: [
              {
                key: 'image',
                control: {
                  can_edit: 0,
                  can_view: 1
                },
                extra_info: null
              }
            ],
            modules: [
              {
                module_id: 0,
                module_type: 'image',
                data: {
                  images: []
                },
                ref_language_data: null
              },
              {
                module_id: 0,
                module_type: 'language',
                data: {
                  name: ''
                },
                ref_language_data: null
              },
              {
                module_id: 0,
                module_type: 'departure_poi',
                data: {
                  map: {
                    address_desc: '',
                    amap_code: '',
                    area_data: [],
                    city_name: '',
                    country_name: '',
                    custom_title_multilang: '',
                    is_customized_area: 0,
                    location: '',
                    location_original: '',
                    name: '',
                    place_id: '',
                    poi_id: 0,
                    supply_api_mapping_key: '',
                    title_multilang: '',
                    type: -1,
                    version: 'v3'
                  },
                  time_select_from_to: [
                    {
                      from: {
                        h: '',
                        m: ''
                      },
                      to: {
                        h: '',
                        m: ''
                      }
                    }
                  ]
                },
                ref_language_data: null
              }
            ]
          }
        ],
        card_info: null
      },
      {
        instance_basic_id: 0,
        data_index: 0,
        instance_index: 0,
        itinerary_type: 'return',
        instance_custom_infos: [
          {
            custom_instance_id: 0,
            custom_instance_type: 'return_custom',
            custom_instance_json: {
              departure_reminder_list: [
                {
                  data: {
                    time: 1
                  },
                  value: 0
                }
              ],
              departure_type: 0,
              time_select: [
                {
                  h: '',
                  m: ''
                }
              ]
            },
            attr_controls: [
              {
                key: 'return_type',
                control: {
                  can_edit: 0,
                  can_view: 1
                },
                extra_info: null
              }
            ]
          }
        ],
        instance_detail_infos: [
          {
            detail_instance_id: 0,
            detail_instance_type: 'return_detail',
            detail_instance_index: 0,
            attr_controls: [
              {
                key: 'image',
                control: {
                  can_edit: 0,
                  can_view: 1
                },
                extra_info: null
              }
            ],
            modules: [
              {
                module_id: 0,
                module_type: 'image',
                data: {
                  images: []
                },
                ref_language_data: null
              },
              {
                module_id: 0,
                module_type: 'language',
                data: {
                  name: ''
                },
                ref_language_data: null
              },
              {
                module_id: 0,
                module_type: 'return_poi',
                data: {
                  map: {
                    address_desc: '',
                    amap_code: '',
                    area_data: [],
                    city_name: '',
                    country_name: '',
                    custom_title_multilang: '',
                    is_customized_area: 0,
                    location: '',
                    location_original: '',
                    name: '',
                    place_id: '',
                    poi_id: 0,
                    supply_api_mapping_key: '',
                    title_multilang: '',
                    type: -1,
                    version: 'v3'
                  },
                  time_select_from_to: [
                    {
                      from: {
                        h: '',
                        m: ''
                      },
                      to: {
                        h: '',
                        m: ''
                      }
                    }
                  ]
                },
                ref_language_data: null
              }
            ]
          }
        ],
        card_info: null
      }
    ],
    enums: {
      departure: {
        sync_supplier_name: {
          options: [
            {
              value: -1,
              label: 'Bokfun____',
              value_string: 'bokfun',
              use_value_type: 'value_string',
              children: null
            }
          ]
        },
        edit_type: {
          options: [
            {
              value: 1,
              label: 'Setting manually____',
              children: null
            },
            {
              value: 2,
              label: 'Sync via API____',
              children: null
            }
          ]
        },
        departure_type: {
          options: [
            {
              value: 1,
              label: 'Meet-up only',
              desc: 'Customers need to go to the meet-up point on their own',
              children: null
            },
            {
              value: 2,
              label: 'Pick-up only',
              desc: 'Customers will be picked up from their accomodation',
              children: null
            },
            {
              value: 3,
              label: 'Meet-up and pick-up both available',
              desc: 'Customer can choose either one',
              children: null
            }
          ]
        },
        departure_reminder_list: {
          options: [
            {
              value: 1,
              label: 'Look for the guide with a Klook sign',
              desc: '',
              data: {
                time: 0
              }
            },
            {
              value: 2,
              label: "Latecomers or no-shows can't be refunded",
              desc: '',
              data: {
                time: 30
              }
            },
            {
              value: 3,
              label: 'See the package details for pick-up fees',
              desc: '',
              data: {
                time: 30
              }
            },
            {
              value: 4,
              label: 'This is a shared transfer and pick up could be early or late',
              desc: '',
              data: {
                time: 30
              }
            },
            {
              value: 5,
              label: 'Enter your hotel name and address at the checkout page',
              desc: '',
              data: {
                time: 30
              }
            },
            {
              value: 6,
              label: 'The operator will re-confirm your pick-up time in advance',
              desc: '',
              data: {
                time: 30
              }
            },
            {
              value: 7,
              label: 'Please arrive at the location {num} mins before the departure time',
              desc: '',
              data: {
                time: 45
              }
            }
          ]
        }
      },
      return: {
        return_type: {
          options: [
            {
              value: 0,
              label: 'No, tour ends at the last stop',
              desc: 'Customers will leave directly after the last stop',
              children: null
            },
            {
              value: 1,
              label: 'Yes, to drop-off point',
              desc: 'Transport provided to a drop-off point decided by the merchant',
              children: null
            },
            {
              value: 2,
              label: "Yes, to customers' accomodation",
              desc: "Transport provided to customers' hotel, airbnb etc",
              children: null
            }
          ]
        }
      },
      attraction: {
        admission_included_type: {
          options: [
            {
              value: 1,
              label: 'Required and included',
              desc: 'Ticket price is included in the tour',
              children: null
            },
            {
              value: 2,
              label: 'Required, but not included',
              desc: 'Customers must buy it either online or on site',
              children: null
            },
            {
              value: 3,
              label: 'Free entry',
              desc: 'No ticket is required at all',
              children: null
            },
            {
              value: 4,
              label: 'Required, and can be included or not included.',
              desc: "You'll be able to set different prices and inventory later on",
              children: null
            }
          ]
        },
        unique_feature: {
          options: [
            {
              value: 10001,
              label: 'Outdoor adventures',
              desc: 'Outdoor adventures',
              children: [
                {
                  value: 1,
                  label: 'Jungle swing',
                  desc: 'Jungle swing',
                  children: null
                },
                {
                  value: 2,
                  label: 'Sailing',
                  desc: 'Sailing',
                  children: null
                },
                {
                  value: 3,
                  label: 'Dune bashing',
                  desc: 'Dune bashing',
                  children: null
                },
                {
                  value: 4,
                  label: 'River tracing',
                  desc: 'River tracing',
                  children: null
                },
                {
                  value: 5,
                  label: 'Abseiling',
                  desc: 'Abseiling',
                  children: null
                },
                {
                  value: 6,
                  label: 'Extreme sports',
                  desc: 'Extreme sports',
                  children: null
                },
                {
                  value: 7,
                  label: 'High-altitude trekking',
                  desc: 'High-altitude trekking',
                  children: null
                },
                {
                  value: 8,
                  label: 'Jeep safari',
                  desc: 'Jeep safari',
                  children: null
                },
                {
                  value: 9,
                  label: 'Hot air balloon',
                  desc: 'Hot air balloon',
                  children: null
                },
                {
                  value: 10,
                  label: 'Rock climbing',
                  desc: 'Rock climbing',
                  children: null
                },
                {
                  value: 11,
                  label: 'Quad biking',
                  desc: 'Quad biking',
                  children: null
                },
                {
                  value: 12,
                  label: 'Cycling',
                  desc: 'Cycling',
                  children: null
                },
                {
                  value: 13,
                  label: 'Picnic',
                  desc: 'Picnic',
                  children: null
                },
                {
                  value: 14,
                  label: 'Sandboarding',
                  desc: 'Sandboarding',
                  children: null
                },
                {
                  value: 15,
                  label: 'Zipline experience',
                  desc: 'Zipline experience',
                  children: null
                },
                {
                  value: 16,
                  label: 'Cave exploration',
                  desc: 'Cave exploration',
                  children: null
                },
                {
                  value: 17,
                  label: 'Island hopping',
                  desc: 'Island hopping',
                  children: null
                },
                {
                  value: 18,
                  label: 'Bridge climbing',
                  desc: 'Bridge climbing',
                  children: null
                },
                {
                  value: 19,
                  label: 'Jetskiing',
                  desc: 'Jetskiing',
                  children: null
                },
                {
                  value: 20,
                  label: 'Helicopter riding',
                  desc: 'Helicopter riding',
                  children: null
                },
                {
                  value: 21,
                  label: 'ATV riding',
                  desc: 'ATV riding',
                  children: null
                },
                {
                  value: 22,
                  label: 'Diving',
                  desc: 'Diving',
                  children: null
                },
                {
                  value: 23,
                  label: 'Kayaking',
                  desc: 'Kayaking',
                  children: null
                },
                {
                  value: 24,
                  label: 'Snorkeling',
                  desc: 'Snorkeling',
                  children: null
                },
                {
                  value: 25,
                  label: 'Bouldering',
                  desc: 'Bouldering',
                  children: null
                },
                {
                  value: 26,
                  label: 'Hiking',
                  desc: 'Hiking',
                  children: null
                },
                {
                  value: 27,
                  label: 'Trekking',
                  desc: 'Trekking',
                  children: null
                },
                {
                  value: 28,
                  label: 'Ice climbing',
                  desc: 'Ice climbing',
                  children: null
                },
                {
                  value: 29,
                  label: 'Cliff diving',
                  desc: 'Cliff diving',
                  children: null
                },
                {
                  value: 30,
                  label: 'Catapult experience',
                  desc: 'Catapult experience',
                  children: null
                },
                {
                  value: 31,
                  label: 'Rail bike',
                  desc: 'Rail bike',
                  children: null
                },
                {
                  value: 32,
                  label: 'Heli hiking',
                  desc: 'Heli hiking',
                  children: null
                },
                {
                  value: 33,
                  label: 'Jet boat',
                  desc: 'Jet boat',
                  children: null
                },
                {
                  value: 34,
                  label: 'Sea walking',
                  desc: 'Sea walking',
                  children: null
                },
                {
                  value: 35,
                  label: 'Tubing',
                  desc: 'Tubing',
                  children: null
                },
                {
                  value: 36,
                  label: 'Parasailing',
                  desc: 'Parasailing',
                  children: null
                },
                {
                  value: 37,
                  label: 'Swimming',
                  desc: 'Swimming',
                  children: null
                },
                {
                  value: 38,
                  label: 'Banana boat',
                  desc: 'Banana boat',
                  children: null
                },
                {
                  value: 39,
                  label: 'Paragliding',
                  desc: 'Paragliding',
                  children: null
                },
                {
                  value: 40,
                  label: 'Skydiving',
                  desc: 'Skydiving',
                  children: null
                },
                {
                  value: 41,
                  label: 'Bungee jumping',
                  desc: 'Bungee jumping',
                  children: null
                }
              ]
            },
            {
              value: 10002,
              label: 'Cultural experiences',
              desc: 'Cultural experiences',
              children: [
                {
                  value: 42,
                  label: 'Pottery',
                  desc: 'Pottery',
                  children: null
                },
                {
                  value: 43,
                  label: 'Workshop and class',
                  desc: 'Workshop and class',
                  children: null
                },
                {
                  value: 44,
                  label: 'Qipao experience',
                  desc: 'Qipao experience',
                  children: null
                },
                {
                  value: 45,
                  label: 'Hanbok experience',
                  desc: 'Hanbok experience',
                  children: null
                },
                {
                  value: 46,
                  label: 'Kimono experience',
                  desc: 'Kimono experience',
                  children: null
                },
                {
                  value: 47,
                  label: 'Cosplay',
                  desc: 'Cosplay',
                  children: null
                },
                {
                  value: 48,
                  label: 'Traditional costume experience',
                  desc: 'Traditional costume experience',
                  children: null
                },
                {
                  value: 49,
                  label: 'Shopping',
                  desc: 'Shopping',
                  children: null
                },
                {
                  value: 50,
                  label: 'Tea making',
                  desc: 'Tea making',
                  children: null
                },
                {
                  value: 51,
                  label: 'Gold panning',
                  desc: 'Gold panning',
                  children: null
                },
                {
                  value: 52,
                  label: 'Fruit picking',
                  desc: 'Fruit picking',
                  children: null
                },
                {
                  value: 53,
                  label: 'Live performance',
                  desc: 'Live performance',
                  children: null
                }
              ]
            },
            {
              value: 10003,
              label: 'Wildlife and nature',
              desc: 'Wildlife and nature',
              children: [
                {
                  value: 54,
                  label: 'Dolphin watching',
                  desc: 'Dolphin watching',
                  children: null
                },
                {
                  value: 55,
                  label: 'Waterfall hike',
                  desc: 'Waterfall hike',
                  children: null
                },
                {
                  value: 56,
                  label: 'Elephant feeding',
                  desc: 'Elephant feeding',
                  children: null
                },
                {
                  value: 57,
                  label: 'Safari',
                  desc: 'Safari',
                  children: null
                },
                {
                  value: 58,
                  label: 'Sunset party',
                  desc: 'Sunset party',
                  children: null
                },
                {
                  value: 59,
                  label: 'Sunrise watching',
                  desc: 'Sunrise watching',
                  children: null
                },
                {
                  value: 60,
                  label: 'Whale watching',
                  desc: 'Whale watching',
                  children: null
                },
                {
                  value: 61,
                  label: 'Animal encounters',
                  desc: 'Animal encounters',
                  children: null
                },
                {
                  value: 62,
                  label: 'Animal feeding',
                  desc: 'Animal feeding',
                  children: null
                },
                {
                  value: 63,
                  label: 'Fishing',
                  desc: 'Fishing',
                  children: null
                },
                {
                  value: 64,
                  label: 'Stargazing',
                  desc: 'Stargazing',
                  children: null
                },
                {
                  value: 65,
                  label: 'Horse riding',
                  desc: 'Horse riding',
                  children: null
                },
                {
                  value: 66,
                  label: 'Canopy walk',
                  desc: 'Canopy walk',
                  children: null
                },
                {
                  value: 67,
                  label: 'Camel riding',
                  desc: 'Camel riding',
                  children: null
                },
                {
                  value: 68,
                  label: 'Night fishing',
                  desc: 'Night fishing',
                  children: null
                },
                {
                  value: 200,
                  label: 'Tulip fields',
                  desc: 'Tulip',
                  children: null
                },
                {
                  value: 201,
                  label: 'Lavender fields',
                  desc: 'Lavender',
                  children: null
                },
                {
                  value: 202,
                  label: 'Cherry blossoms',
                  desc: 'Cherry Blossom',
                  children: null
                },
                {
                  value: 203,
                  label: 'Firefly watching',
                  desc: 'Fireflies',
                  children: null
                },
                {
                  value: 204,
                  label: 'Maple leaf',
                  desc: 'Maple Leaf',
                  children: null
                },
                {
                  value: 205,
                  label: 'Northern lights',
                  desc: 'Northern Lights',
                  children: null
                }
              ]
            },
            {
              value: 10004,
              label: 'Food and drinks',
              desc: 'Food and drinks',
              children: [
                {
                  value: 69,
                  label: 'Local food tasting',
                  desc: 'Local food tasting',
                  children: null
                },
                {
                  value: 70,
                  label: 'Dining',
                  desc: 'Dining',
                  children: null
                },
                {
                  value: 71,
                  label: 'Street food tasting',
                  desc: 'Street food tasting',
                  children: null
                },
                {
                  value: 72,
                  label: 'Whiskey tasting',
                  desc: 'Whiskey tasting',
                  children: null
                },
                {
                  value: 73,
                  label: 'Matcha tasting',
                  desc: 'Matcha tasting',
                  children: null
                },
                {
                  value: 74,
                  label: 'Beer tasting',
                  desc: 'Beer tasting',
                  children: null
                },
                {
                  value: 75,
                  label: 'Chocolate tasting',
                  desc: 'Chocolate tasting',
                  children: null
                },
                {
                  value: 76,
                  label: 'Cheese tasting',
                  desc: 'Cheese tasting',
                  children: null
                },
                {
                  value: 77,
                  label: 'Wine tasting',
                  desc: 'Wine tasting',
                  children: null
                },
                {
                  value: 78,
                  label: 'Virtual dining',
                  desc: 'Virtual dining',
                  children: null
                }
              ]
            },
            {
              value: 10005,
              label: 'Wellness and relaxation',
              desc: 'Wellness and relaxation',
              children: [
                {
                  value: 79,
                  label: 'Wellness experience',
                  desc: 'Wellness experience',
                  children: null
                },
                {
                  value: 80,
                  label: 'Mud bath',
                  desc: 'Mud bath',
                  children: null
                },
                {
                  value: 81,
                  label: 'Yoga',
                  desc: 'Yoga',
                  children: null
                },
                {
                  value: 82,
                  label: 'Standup paddleboarding',
                  desc: 'Standup paddleboarding',
                  children: null
                },
                {
                  value: 83,
                  label: 'Mineral bath',
                  desc: 'Mineral bath',
                  children: null
                },
                {
                  value: 84,
                  label: 'Mineral therapy',
                  desc: 'Mineral therapy',
                  children: null
                },
                {
                  value: 85,
                  label: 'Elephant massage',
                  desc: 'Elephant massage',
                  children: null
                },
                {
                  value: 86,
                  label: 'Fish pedicure',
                  desc: 'Fish pedicure',
                  children: null
                },
                {
                  value: 206,
                  label: 'Meditation',
                  desc: 'Meditation',
                  children: null
                }
              ]
            },
            {
              value: 10006,
              label: 'Entertainment and nightlife',
              desc: 'Entertainment and nightlife',
              children: [
                {
                  value: 87,
                  label: 'Karaoke',
                  desc: 'Karaoke',
                  children: null
                },
                {
                  value: 88,
                  label: 'Escape room',
                  desc: 'Escape room',
                  children: null
                },
                {
                  value: 89,
                  label: 'Pub crawl',
                  desc: 'Pub crawl',
                  children: null
                },
                {
                  value: 90,
                  label: 'Scavenger hunt',
                  desc: 'Scavenger hunt',
                  children: null
                },
                {
                  value: 91,
                  label: 'Indoor games',
                  desc: 'Indoor games',
                  children: null
                },
                {
                  value: 92,
                  label: 'Shisha experience',
                  desc: 'Shisha experience',
                  children: null
                },
                {
                  value: 93,
                  label: 'Bar hopping',
                  desc: 'Bar hopping',
                  children: null
                }
              ]
            },
            {
              value: 10007,
              label: 'Sports and games',
              desc: 'Sports and games',
              children: [
                {
                  value: 94,
                  label: 'Golfing',
                  desc: 'Golfing',
                  children: null
                },
                {
                  value: 95,
                  label: 'Archery',
                  desc: 'Archery',
                  children: null
                },
                {
                  value: 96,
                  label: 'Surfing',
                  desc: 'Surfing',
                  children: null
                },
                {
                  value: 97,
                  label: 'Water sports',
                  desc: 'Water sports',
                  children: null
                },
                {
                  value: 98,
                  label: 'Driving',
                  desc: 'Driving',
                  children: null
                },
                {
                  value: 99,
                  label: 'Kart racing',
                  desc: 'Kart racing',
                  children: null
                },
                {
                  value: 100,
                  label: 'Skiing',
                  desc: 'Skiing',
                  children: null
                },
                {
                  value: 101,
                  label: 'Horse race watching',
                  desc: 'Horse race watching',
                  children: null
                },
                {
                  value: 102,
                  label: 'Sledding',
                  desc: 'Sledding',
                  children: null
                },
                {
                  value: 103,
                  label: 'Snow tubing',
                  desc: 'Snow tubing',
                  children: null
                },
                {
                  value: 104,
                  label: 'Beach volleyball',
                  desc: 'Beach volleyball',
                  children: null
                }
              ]
            },
            {
              value: 10008,
              label: 'Others',
              desc: 'Others',
              children: [
                {
                  value: 105,
                  label: 'For autumn',
                  desc: 'For Autumn',
                  children: null
                },
                {
                  value: 106,
                  label: 'For spring',
                  desc: 'For spring',
                  children: null
                },
                {
                  value: 107,
                  label: 'For summer',
                  desc: 'For summer',
                  children: null
                },
                {
                  value: 108,
                  label: 'For winter',
                  desc: 'For winter',
                  children: null
                },
                {
                  value: 210,
                  label: 'Instagrammable spot',
                  desc: 'Instagrammable spot',
                  children: null
                },
                {
                  value: 211,
                  label: 'Observatory deck',
                  desc: 'Observatory deck',
                  children: null
                }
              ]
            }
          ]
        }
      },
      transport: {
        vehicle_type: {
          options: [
            {
              value: 1,
              label: 'Car',
              desc: '',
              children: null
            },
            {
              value: 2,
              label: 'Bus',
              desc: '',
              children: null
            },
            {
              value: 3,
              label: 'Boat',
              desc: '',
              children: null
            },
            {
              value: 4,
              label: 'Train',
              desc: '',
              children: null
            },
            {
              value: 5,
              label: 'Riverboat',
              desc: '',
              children: null
            },
            {
              value: 6,
              label: 'Yacht',
              desc: '',
              children: null
            },
            {
              value: 7,
              label: 'On foot',
              desc: '',
              children: null
            },
            {
              value: 8,
              label: 'Motorbike',
              desc: '',
              children: null
            },
            {
              value: 9,
              label: 'Double-decker bus',
              desc: '',
              children: null
            },
            {
              value: 10,
              label: 'Coach',
              desc: '',
              children: null
            },
            {
              value: 11,
              label: 'Minibus',
              desc: '',
              children: null
            },
            {
              value: 12,
              label: 'Tuk-tuk',
              desc: '',
              children: null
            },
            {
              value: 13,
              label: 'Longtail boat',
              desc: '',
              children: null
            },
            {
              value: 14,
              label: 'Catamaran',
              desc: '',
              children: null
            },
            {
              value: 15,
              label: 'Speedboat',
              desc: '',
              children: null
            },
            {
              value: 16,
              label: 'Cruise',
              desc: '',
              children: null
            },
            {
              value: 17,
              label: 'Ferry',
              desc: '',
              children: null
            },
            {
              value: 18,
              label: 'Bullet train',
              desc: '',
              children: null
            },
            {
              value: 19,
              label: 'Small van',
              desc: '',
              children: null
            },
            {
              value: 20,
              label: 'Van',
              desc: '',
              children: null
            }
          ]
        }
      },
      meal: {
        meal_type: {
          options: [
            {
              value: 1,
              label: 'Breakfast',
              desc: '',
              children: null
            },
            {
              value: 2,
              label: 'Brunch',
              desc: '',
              children: null
            },
            {
              value: 3,
              label: 'Lunch',
              desc: '',
              children: null
            },
            {
              value: 4,
              label: 'Afternoon tea',
              desc: '',
              children: null
            },
            {
              value: 5,
              label: 'Dinner',
              desc: '',
              children: null
            },
            {
              value: 6,
              label: 'Snacks and drinks',
              desc: '',
              children: null
            },
            {
              value: 7,
              label: 'Drinks',
              desc: '',
              children: null
            },
            {
              value: 8,
              label: 'Snacks',
              desc: '',
              children: null
            },
            {
              value: 9,
              label: 'Late-night meal',
              desc: '',
              children: null
            }
          ]
        },
        cuisine_type: {
          options: [
            {
              value: 1,
              label: 'Barbecue',
              desc: 'JBarbecue',
              children: null
            },
            {
              value: 2,
              label: 'Fusion',
              desc: 'Fusion',
              children: null
            },
            {
              value: 3,
              label: 'Western',
              desc: 'Western',
              children: null
            },
            {
              value: 4,
              label: 'Asian',
              desc: 'Asian',
              children: null
            },
            {
              value: 5,
              label: 'Multiple cuisine options',
              desc: 'Multiple cuisine options',
              children: null
            },
            {
              value: 6,
              label: 'Buffet',
              desc: 'Buffet',
              children: null
            },
            {
              value: 7,
              label: 'Vietnamese',
              desc: 'Vietnamese',
              children: null
            },
            {
              value: 8,
              label: 'Vegetarian options',
              desc: 'Vegetarian options',
              children: null
            },
            {
              value: 9,
              label: 'Vegan options',
              desc: 'Vegan options',
              children: null
            },
            {
              value: 10,
              label: 'Caribbean',
              desc: 'Caribbean',
              children: null
            },
            {
              value: 11,
              label: 'Moroccan',
              desc: 'Moroccan',
              children: null
            },
            {
              value: 12,
              label: 'Ethiopian',
              desc: 'Ethiopian',
              children: null
            },
            {
              value: 13,
              label: 'Peruvian',
              desc: 'Peruvian',
              children: null
            },
            {
              value: 14,
              label: 'Brazilian',
              desc: 'Brazilian',
              children: null
            },
            {
              value: 15,
              label: 'Mediterranean',
              desc: 'Mediterranean',
              children: null
            },
            {
              value: 16,
              label: 'Lebanese',
              desc: 'Lebanese',
              children: null
            },
            {
              value: 17,
              label: 'Turkish',
              desc: 'Turkish',
              children: null
            },
            {
              value: 18,
              label: 'American',
              desc: 'American',
              children: null
            },
            {
              value: 19,
              label: 'Halal',
              desc: 'Halal',
              children: null
            },
            {
              value: 20,
              label: 'Middle Eastern',
              desc: 'Middle Eastern',
              children: null
            },
            {
              value: 21,
              label: 'Korean',
              desc: 'Korean',
              children: null
            },
            {
              value: 22,
              label: 'Greek',
              desc: 'Greek',
              children: null
            },
            {
              value: 23,
              label: 'Spanish',
              desc: 'Spanish',
              children: null
            },
            {
              value: 24,
              label: 'French',
              desc: 'French',
              children: null
            },
            {
              value: 25,
              label: 'Thai',
              desc: 'Thai',
              children: null
            },
            {
              value: 26,
              label: 'Indian',
              desc: 'Indian',
              children: null
            },
            {
              value: 27,
              label: 'Mexican',
              desc: 'Mexican',
              children: null
            },
            {
              value: 28,
              label: 'Japanese',
              desc: 'Japanese',
              children: null
            },
            {
              value: 29,
              label: 'Chinese',
              desc: 'Chinese',
              children: null
            },
            {
              value: 30,
              label: 'Italian',
              desc: 'Italian',
              children: null
            }
          ]
        }
      },
      accommodation: {
        accommodation_type: {
          options: [
            {
              value: 1,
              label: 'Hotel',
              desc: '',
              children: null
            },
            {
              value: 2,
              label: 'Guesthouse',
              desc: '',
              children: null
            },
            {
              value: 3,
              label: 'Apartment',
              desc: '',
              children: null
            },
            {
              value: 4,
              label: 'Hostel',
              desc: '',
              children: null
            },
            {
              value: 5,
              label: 'Resort',
              desc: '',
              children: null
            },
            {
              value: 6,
              label: 'Capsule',
              desc: '',
              children: null
            }
          ]
        },
        bed_type: {
          options: [
            {
              value: 1,
              label: '1 single bed',
              desc: '',
              children: null
            },
            {
              value: 2,
              label: '2 single beds',
              desc: '',
              children: null
            },
            {
              value: 3,
              label: '3 single beds',
              desc: '',
              children: null
            },
            {
              value: 4,
              label: 'Queen-size bed (for 2 people)',
              desc: '',
              children: null
            }
          ]
        },
        quantity_limitation: {
          options: [
            {
              value: 1,
              label: '1',
              desc: '',
              children: null
            },
            {
              value: 2,
              label: '2',
              desc: '',
              children: null
            },
            {
              value: 3,
              label: '3',
              desc: '',
              children: null
            },
            {
              value: 4,
              label: '4',
              desc: '',
              children: null
            },
            {
              value: 5,
              label: '5',
              desc: '',
              children: null
            },
            {
              value: 6,
              label: '6',
              desc: '',
              children: null
            },
            {
              value: 7,
              label: '7',
              desc: '',
              children: null
            },
            {
              value: 8,
              label: '8',
              desc: '',
              children: null
            },
            {
              value: 9,
              label: '9',
              desc: '',
              children: null
            },
            {
              value: 10,
              label: '10',
              desc: '',
              children: null
            }
          ]
        },
        property_rating: {
          options: [
            {
              value: 2,
              label: '2 stars or below (Economy)',
              desc: '',
              children: null
            },
            {
              value: 3,
              label: '3 stars (Standard)',
              desc: '',
              children: null
            },
            {
              value: 4,
              label: '4 stars (Premium)',
              desc: '',
              children: null
            },
            {
              value: 5,
              label: '5 stars (Luxury)',
              desc: '',
              children: null
            }
          ]
        },
        bathroom_facilities: {
          options: [
            {
              value: 1,
              label: 'Private bathroom',
              desc: 'Shared bathroom with other guests',
              children: null
            },
            {
              value: 2,
              label: 'Shared bathroom',
              desc: 'Private bathroom in the room',
              children: null
            }
          ]
        }
      }
    },
    audios: {},
    activity_departure_city: {
      area_id: 23301,
      area_name: 'Shenzhen City',
      node_type: 3,
      location: '22.543096,114.057865',
      country_area_id: 1020
    },
    merchant_currency: 'TWD',
    stock_out_type: 'KLOOKCODE',
    audio_permission: 0,
    language: 'en_US'
  }
}

export const itineraryResult = {
  success: true,
  error: {
    code: '',
    message: ''
  },
  result: {
    itinerary_id: 718,
    extra_id: 99914,
    extra_type: 'SPU',
    tour_days: 2,
    tour_type: 'multi_day',
    days: [
      {
        instances: [
          {
            instance_basic_id: 23,
            data_index: 0,
            instance_index: 0,
            itinerary_type: 'departure',
            instance_custom_infos: [
              {
                custom_instance_id: 17,
                custom_instance_type: 'departure_custom',
                custom_instance_json: {
                  departure_reminder_list: [
                    {
                      value: 1
                    },
                    {
                      value: 2
                    },
                    {
                      data: {
                        time: 3
                      },
                      value: 3
                    }
                  ],
                  departure_type: 3,
                  api_sync_status: 2,
                  edit_type: 1,
                  time_select: [
                    {
                      h: '08',
                      m: '08'
                    },
                    {
                      h: '08',
                      hours_num: 8,
                      m: '09',
                      mins_num: 9
                    },
                    {
                      h: '11',
                      hours_num: 8,
                      m: '12',
                      mins_num: 10
                    }
                  ]
                },
                attr_controls: null
              }
            ],
            instance_detail_infos: [
              {
                detail_instance_id: 33,
                detail_instance_type: 'departure_pick_up_detail',
                detail_instance_index: 0,
                attr_controls: null,
                modules: [
                  {
                    module_id: 33,
                    module_type: 'image',
                    data: {
                      images: []
                    },
                    ref_language_data: null
                  },
                  {
                    module_id: 26,
                    module_type: 'language',
                    data: {
                      name: ''
                    },
                    ref_language_data: {
                      name: ''
                    }
                  },
                  {
                    module_id: 33,
                    module_type: 'departure_poi',
                    data: {
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: [
                          {
                            address_desc: '',
                            administrative_level: 2,
                            amap_code: '',
                            area_id: 23301,
                            customized_config: {
                              customized_area_name: '',
                              extra_fee: {
                                price_currency: 'CNY',
                                price_value: '123'
                              },
                              is_selected: 1
                            },
                            google_place_id: 'ChIJkVLh0Aj0AzQRyYCStw1V7v0',
                            id: 0,
                            location: '22.543096,114.057865',
                            location_name: 'Shenzhen City',
                            location_original: '',
                            map_type: 1,
                            parent_name: 'Guangdong',
                            path: [364890, 2001, 1020, 105],
                            pick_up_type: 2,
                            supply_api_mapping_key: ''
                          }
                        ],
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: '',
                        is_customized_area: 0,
                        location: '',
                        location_original: '',
                        name: '',
                        place_id: '',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: '',
                        type: 0,
                        version: 'v3'
                      },
                      time_select_from_to: [
                        {
                          from: {
                            h: '23',
                            m: '09'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '18',
                            m: '01'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '19',
                            m: '02'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ]
                    },
                    ref_language_data: {
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: [
                          {
                            address_desc: '',
                            administrative_level: 2,
                            amap_code: '',
                            area_id: 23301,
                            customized_config: {
                              customized_area_name: '',
                              extra_fee: {
                                price_currency: 'CNY',
                                price_value: '123'
                              },
                              is_selected: 1
                            },
                            google_place_id: 'ChIJkVLh0Aj0AzQRyYCStw1V7v0',
                            id: 0,
                            location: '22.543096,114.057865',
                            location_name: 'Shenzhen City',
                            location_original: '',
                            map_type: 1,
                            parent_name: 'Guangdong',
                            path: [364890, 2001, 1020, 105],
                            pick_up_type: 2,
                            supply_api_mapping_key: ''
                          }
                        ],
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: '',
                        is_customized_area: 0,
                        location: '',
                        location_original: '',
                        name: '',
                        place_id: '',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: '',
                        type: 0,
                        version: 'v3'
                      },
                      time_select_from_to: [
                        {
                          from: {
                            h: '23',
                            m: '09'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '18',
                            m: '01'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '19',
                            m: '02'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ]
                    }
                  }
                ]
              },
              {
                detail_instance_id: 34,
                detail_instance_type: 'departure_meet_up_detail',
                detail_instance_index: 1,
                attr_controls: null,
                modules: [
                  {
                    module_id: 34,
                    module_type: 'image',
                    data: {
                      images: []
                    },
                    ref_language_data: null
                  },
                  {
                    module_id: 27,
                    module_type: 'language',
                    data: {
                      name: ''
                    },
                    ref_language_data: {
                      name: ''
                    }
                  },
                  {
                    module_id: 138,
                    module_type: 'departure_poi',
                    data: {
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: [],
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: 'ez',
                        is_customized_area: 0,
                        location: '51.61873130344353,-0.019051444457998112',
                        location_original: '',
                        name: '',
                        place_id: 'ChIJk62ZwBUedkgRM-xHVeINY9E',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: '10 Hurst Ave',
                        type: 0,
                        version: 'v3'
                      },
                      time_select_from_to: [
                        {
                          from: {
                            h: '22',
                            m: '16'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '19',
                            m: '01'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '19',
                            m: '02'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ]
                    },
                    ref_language_data: {
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: [],
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: 'ez',
                        is_customized_area: 0,
                        location: '51.61873130344353,-0.019051444457998112',
                        location_original: '',
                        name: '',
                        place_id: 'ChIJk62ZwBUedkgRM-xHVeINY9E',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: '10 Hurst Ave',
                        type: 0,
                        version: 'v3'
                      },
                      time_select_from_to: [
                        {
                          from: {
                            h: '22',
                            m: '16'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '19',
                            m: '01'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '19',
                            m: '02'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ]
                    }
                  }
                ]
              },
              {
                detail_instance_id: 35,
                detail_instance_type: 'departure_meet_up_detail',
                detail_instance_index: 2,
                attr_controls: null,
                modules: [
                  {
                    module_id: 35,
                    module_type: 'image',
                    data: {
                      images: []
                    },
                    ref_language_data: null
                  },
                  {
                    module_id: 28,
                    module_type: 'language',
                    data: {
                      name: ''
                    },
                    ref_language_data: {
                      name: ''
                    }
                  },
                  {
                    module_id: 35,
                    module_type: 'departure_poi',
                    data: {
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: null,
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: "Shang Hai ShiHuang Pu QuPeople's SquareXizang Road (M)96",
                        is_customized_area: 0,
                        location: '31.232067325586893,121.47316455819701',
                        location_original: '',
                        name: '',
                        place_id: 'ChIJJSACOmhwsjURYusH6_NiZ_c',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: "Shang Hai ShiHuang Pu QuPeople's SquareXizang Road (M)96",
                        type: 0,
                        version: ''
                      },
                      time_select_from_to: [
                        {
                          from: {
                            h: '23',
                            m: '11'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '21',
                            m: '03'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '21',
                            m: '05'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ]
                    },
                    ref_language_data: {
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: null,
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: "Shang Hai ShiHuang Pu QuPeople's SquareXizang Road (M)96",
                        is_customized_area: 0,
                        location: '31.232067325586893,121.47316455819701',
                        location_original: '',
                        name: '',
                        place_id: 'ChIJJSACOmhwsjURYusH6_NiZ_c',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: "Shang Hai ShiHuang Pu QuPeople's SquareXizang Road (M)96",
                        type: 0,
                        version: ''
                      },
                      time_select_from_to: [
                        {
                          from: {
                            h: '23',
                            m: '11'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '21',
                            m: '03'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '21',
                            m: '05'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            ],
            card_info: {
              text_icons: [
                {
                  text: 'meet up',
                  icon: 'https://res.klook.com/image/upload/w_400/contact_info_eg_bw6chh.png',
                  icon_desc: 'Pending for API synchronization'
                }
              ],
              card_title: 'Departure info',
              infos: [
                {
                  text: 'Meet-up and pick-up both available * 08 : 08 ,08 : 09   \u0026 more'
                },
                {
                  text: '1 pick-up areas in Shenzhen and 2 meet-up points in London,Shanghai '
                }
              ]
            }
          },
          {
            instance_basic_id: 106,
            data_index: 0,
            instance_index: 1,
            itinerary_type: 'attraction',
            instance_custom_infos: [
              {
                custom_instance_id: 75,
                custom_instance_type: 'attraction_custom',
                custom_instance_json: {
                  admission_included_type: 1,
                  attraction_type: -1,
                  guide_privide: 0,
                  time_duration: {
                    h: 1,
                    m: 1
                  }
                },
                attr_controls: null
              }
            ],
            instance_detail_infos: [
              {
                detail_instance_id: 140,
                detail_instance_type: 'attraction_detail',
                detail_instance_index: 0,
                attr_controls: null,
                modules: [
                  {
                    module_id: 129,
                    module_type: 'image',
                    data: {
                      images: [
                        'https://res.klook.com/image/upload/v1735627627/activities/ojxvrk8xjl0i46zzahyd.jpg'
                      ]
                    },
                    ref_language_data: null
                  },
                  {
                    module_id: 100,
                    module_type: 'language',
                    data: {
                      name: ''
                    },
                    ref_language_data: {
                      name: ''
                    }
                  },
                  {
                    module_id: 131,
                    module_type: 'attraction_poi',
                    data: {
                      audios: null,
                      map: {
                        address_desc: 'Shenzhen Wan',
                        amap_code: '',
                        area_data: [],
                        city_name: 'Hong Kong',
                        country_name: 'Hong Kong',
                        custom_title_multilang: '',
                        is_customized_area: 0,
                        location: '22.4327255,113.8971399',
                        location_original: '',
                        name: '',
                        place_id: 'ChIJQ62au6z6AzQRQPHys-k-BYQ',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: 'Shenzhen Wan',
                        type: -1,
                        version: 'v3'
                      },
                      unique_feature: 1
                    },
                    ref_language_data: {
                      audios: null,
                      map: {
                        address_desc: 'Shenzhen Wan',
                        amap_code: '',
                        area_data: [],
                        city_name: 'Hong Kong',
                        country_name: 'Hong Kong',
                        custom_title_multilang: '',
                        is_customized_area: 0,
                        location: '22.4327255,113.8971399',
                        location_original: '',
                        name: '',
                        place_id: 'ChIJQ62au6z6AzQRQPHys-k-BYQ',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: 'Shenzhen Wan',
                        type: -1,
                        version: 'v3'
                      },
                      unique_feature: 1
                    }
                  }
                ]
              }
            ],
            card_info: null
          },
          {
            instance_basic_id: 107,
            data_index: 0,
            instance_index: 2,
            itinerary_type: 'transport',
            instance_custom_infos: [
              {
                custom_instance_id: 76,
                custom_instance_type: 'transport_custom',
                custom_instance_json: {
                  multi_transport: 0,
                  muti_transport: 0
                },
                attr_controls: null
              }
            ],
            instance_detail_infos: [
              {
                detail_instance_id: 141,
                detail_instance_type: 'transport_detail',
                detail_instance_index: 0,
                attr_controls: null,
                modules: [
                  {
                    module_id: 130,
                    module_type: 'image',
                    data: {
                      images: []
                    },
                    ref_language_data: null
                  },
                  {
                    module_id: 101,
                    module_type: 'language',
                    data: {
                      name: ''
                    },
                    ref_language_data: {
                      name: ''
                    }
                  },
                  {
                    module_id: 132,
                    module_type: 'transport_poi',
                    data: {
                      time_duration: {
                        h: 0,
                        m: 0
                      },
                      time_select_from_to: [
                        {
                          from: {
                            h: '',
                            m: ''
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ],
                      vehicle_type: 1
                    },
                    ref_language_data: {
                      time_duration: {
                        h: 0,
                        m: 0
                      },
                      time_select_from_to: [
                        {
                          from: {
                            h: '',
                            m: ''
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ],
                      vehicle_type: 1
                    }
                  }
                ]
              }
            ],
            card_info: null
          },
          {
            instance_basic_id: 108,
            data_index: 0,
            instance_index: 3,
            itinerary_type: 'meal',
            instance_custom_infos: null,
            instance_detail_infos: [
              {
                detail_instance_id: 142,
                detail_instance_type: 'meal_detail',
                detail_instance_index: 0,
                attr_controls: null,
                modules: [
                  {
                    module_id: 131,
                    module_type: 'image',
                    data: {
                      images: []
                    },
                    ref_language_data: null
                  },
                  {
                    module_id: 133,
                    module_type: 'meal_poi',
                    data: {
                      cuisine_type: [1],
                      expense_included: 0,
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: [],
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: '',
                        is_customized_area: 0,
                        location: '',
                        location_original: '',
                        name: '',
                        place_id: '',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: '',
                        type: -1,
                        version: 'v3'
                      },
                      meal_type: 1,
                      time: {
                        h: 0,
                        m: 2
                      }
                    },
                    ref_language_data: {
                      cuisine_type: [1],
                      expense_included: 0,
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: [],
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: '',
                        is_customized_area: 0,
                        location: '',
                        location_original: '',
                        name: '',
                        place_id: '',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: '',
                        type: -1,
                        version: 'v3'
                      },
                      meal_type: 1,
                      time: {
                        h: 0,
                        m: 2
                      }
                    }
                  }
                ]
              }
            ],
            card_info: null
          }
        ]
      },
      {
        instances: [
          {
            instance_basic_id: 24,
            data_index: 1,
            instance_index: 0,
            itinerary_type: 'return',
            instance_custom_infos: [
              {
                custom_instance_id: 18,
                custom_instance_type: 'return_custom',
                custom_instance_json: {
                  departure_reminder_list: [
                    {
                      data: {
                        time: 1
                      },
                      value: 0
                    }
                  ],
                  departure_type: 0,
                  time_select: [
                    {
                      h: '',
                      m: ''
                    }
                  ]
                },
                attr_controls: null
              }
            ],
            instance_detail_infos: [
              {
                detail_instance_id: 36,
                detail_instance_type: 'return_detail',
                detail_instance_index: 0,
                attr_controls: null,
                modules: [
                  {
                    module_id: 36,
                    module_type: 'image',
                    data: {
                      images: []
                    },
                    ref_language_data: null
                  },
                  {
                    module_id: 29,
                    module_type: 'language',
                    data: {
                      name: '23456'
                    },
                    ref_language_data: {
                      name: '23456'
                    }
                  },
                  {
                    module_id: 139,
                    module_type: 'return_poi',
                    data: {
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: [],
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: '',
                        is_customized_area: 0,
                        location: '',
                        location_original: '',
                        name: '',
                        place_id: '',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: '',
                        type: 0,
                        version: 'v3'
                      },
                      return_type: 0,
                      time_select_from_to: [
                        {
                          from: {
                            h: '22',
                            m: '17'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '19',
                            m: '01'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '21',
                            m: '04'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ]
                    },
                    ref_language_data: {
                      map: {
                        address_desc: '',
                        amap_code: '',
                        area_data: [],
                        city_name: '',
                        country_name: '',
                        custom_title_multilang: '',
                        is_customized_area: 0,
                        location: '',
                        location_original: '',
                        name: '',
                        place_id: '',
                        poi_id: 0,
                        supply_api_mapping_key: '',
                        title_multilang: '',
                        type: 0,
                        version: 'v3'
                      },
                      return_type: 0,
                      time_select_from_to: [
                        {
                          from: {
                            h: '22',
                            m: '17'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '19',
                            m: '01'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        },
                        {
                          from: {
                            h: '21',
                            m: '04'
                          },
                          to: {
                            h: '',
                            m: ''
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            ],
            card_info: {
              card_title: 'Return info',
              infos: [
                {
                  text: 'No, tour ends at the last stop * 22 : 17 ,19 : 01 ,21 : 04 '
                }
              ]
            }
          }
        ]
      }
    ],
    template_sample: [
      {
        instance_basic_id: 0,
        data_index: 0,
        instance_index: 0,
        itinerary_type: 'departure',
        instance_custom_infos: [],
        instance_detail_infos: [],
        card_info: {
          card_title: 'Departure info',
          infos: [
            {
              text: 'Meet up*8:00 AM'
            },
            {
              text: 'JR Tokyo'
            }
          ]
        }
      },
      {
        instance_basic_id: 0,
        data_index: 0,
        instance_index: 0,
        itinerary_type: 'attraction',
        instance_custom_infos: [
          {
            custom_instance_id: 0,
            custom_instance_type: 'attraction_custom',
            custom_instance_json: {
              admission_included_type: -1,
              attraction_type: -1,
              guide_privide: -1,
              time_duration: {
                h: '',
                m: ''
              }
            },
            attr_controls: [
              {
                key: 'departure_type',
                control: {
                  can_edit: 0,
                  can_view: 1
                },
                extra_info: null
              }
            ]
          }
        ],
        instance_detail_infos: [
          {
            detail_instance_id: 0,
            detail_instance_type: 'attraction_detail',
            detail_instance_index: 0,
            attr_controls: [
              {
                key: 'image',
                control: {
                  can_edit: 0,
                  can_view: 1
                },
                extra_info: null
              }
            ],
            modules: [
              {
                module_id: 0,
                module_type: 'image',
                data: {
                  images: []
                },
                ref_language_data: null
              },
              {
                module_id: 0,
                module_type: 'language',
                data: {
                  name: ''
                },
                ref_language_data: null
              },
              {
                module_id: 0,
                module_type: 'attraction_poi',
                data: {
                  audios: null,
                  map: {
                    address_desc: '',
                    amap_code: '',
                    area_data: [],
                    city_name: '',
                    country_name: '',
                    custom_title_multilang: '',
                    is_customized_area: 0,
                    location: '',
                    location_original: '',
                    name: '',
                    place_id: '',
                    poi_id: 0,
                    supply_api_mapping_key: '',
                    title_multilang: '',
                    type: -1,
                    version: 'v3'
                  },
                  unique_feature: -1
                },
                ref_language_data: null
              }
            ]
          }
        ],
        card_info: null
      },
      {
        instance_basic_id: 0,
        data_index: 0,
        instance_index: 0,
        itinerary_type: 'transport',
        instance_custom_infos: [
          {
            custom_instance_id: 0,
            custom_instance_type: 'transport_custom',
            custom_instance_json: {
              muti_transport: 0
            },
            attr_controls: [
              {
                key: 'muti_transport',
                control: {
                  can_edit: 0,
                  can_view: 1
                },
                extra_info: null
              }
            ]
          }
        ],
        instance_detail_infos: [
          {
            detail_instance_id: 0,
            detail_instance_type: 'transport_detail',
            detail_instance_index: 0,
            attr_controls: [
              {
                key: 'image',
                control: {
                  can_edit: 0,
                  can_view: 1
                },
                extra_info: null
              }
            ],
            modules: [
              {
                module_id: 0,
                module_type: 'image',
                data: {
                  images: []
                },
                ref_language_data: null
              },
              {
                module_id: 0,
                module_type: 'language',
                data: {
                  name: ''
                },
                ref_language_data: null
              },
              {
                module_id: 0,
                module_type: 'transport_poi',
                data: {
                  time_duration: {
                    h: '',
                    m: ''
                  },
                  time_select_from_to: [
                    {
                      from: {
                        h: '',
                        m: ''
                      },
                      to: {
                        h: '',
                        m: ''
                      }
                    }
                  ],
                  vehicle_type: -1
                },
                ref_language_data: null
              }
            ]
          }
        ],
        card_info: null
      },
      {
        instance_basic_id: 0,
        data_index: 0,
        instance_index: 0,
        itinerary_type: 'accommodation',
        instance_custom_infos: [],
        instance_detail_infos: [
          {
            detail_instance_id: 0,
            detail_instance_type: 'accommodation_detail',
            detail_instance_index: 0,
            attr_controls: [
              {
                key: 'image',
                control: {
                  can_edit: 0,
                  can_view: 1
                },
                extra_info: null
              }
            ],
            modules: [
              {
                module_id: 0,
                module_type: 'image',
                data: {
                  images: []
                },
                ref_language_data: null
              },
              {
                module_id: 0,
                module_type: 'accommodation_poi',
                data: {
                  accommodation_type: -1,
                  bathroom_facilities: -1,
                  bed_type: [],
                  map: {
                    address_desc: '',
                    amap_code: '',
                    area_data: [],
                    city_name: '',
                    country_name: '',
                    custom_title_multilang: '',
                    is_customized_area: 0,
                    location: '',
                    location_original: '',
                    name: '',
                    place_id: '',
                    poi_id: 0,
                    supply_api_mapping_key: '',
                    title_multilang: '',
                    type: -1,
                    version: 'v3'
                  },
                  property_rating: -1,
                  quantity_limitation: -1
                },
                ref_language_data: null
              }
            ]
          }
        ],
        card_info: null
      },
      {
        instance_basic_id: 0,
        data_index: 0,
        instance_index: 0,
        itinerary_type: 'meal',
        instance_custom_infos: [],
        instance_detail_infos: [
          {
            detail_instance_id: 0,
            detail_instance_type: 'meal_detail',
            detail_instance_index: 0,
            attr_controls: [
              {
                key: 'image',
                control: {
                  can_edit: 0,
                  can_view: 1
                },
                extra_info: null
              }
            ],
            modules: [
              {
                module_id: 0,
                module_type: 'image',
                data: {
                  images: []
                },
                ref_language_data: null
              },
              {
                module_id: 0,
                module_type: 'meal_poi',
                data: {
                  cuisine_type: [0],
                  expense_included: 0,
                  map: {
                    address_desc: '',
                    amap_code: '',
                    area_data: [],
                    city_name: '',
                    country_name: '',
                    custom_title_multilang: '',
                    is_customized_area: 0,
                    location: '',
                    location_original: '',
                    name: '',
                    place_id: '',
                    poi_id: 0,
                    supply_api_mapping_key: '',
                    title_multilang: '',
                    type: -1,
                    version: 'v3'
                  },
                  meal_type: 0,
                  time: {
                    h: '',
                    m: ''
                  }
                },
                ref_language_data: null
              }
            ]
          }
        ],
        card_info: null
      },
      {
        instance_basic_id: 0,
        data_index: 0,
        instance_index: 0,
        itinerary_type: 'return',
        instance_custom_infos: [],
        instance_detail_infos: [],
        card_info: {
          card_title: 'Return Info',
          infos: [
            {
              text: 'Drop up * 8:00 AM'
            }
          ]
        }
      }
    ],
    enums: {
      departure: {
        departure_type: {
          options: [
            {
              value: 1,
              label: 'Meet-up only',
              desc: 'Customers need to go to the meet-up point on their own',
              children: null
            },
            {
              value: 2,
              label: 'Pick-up only',
              desc: 'Customers will be picked up from their accomodation',
              children: null
            },
            {
              value: 3,
              label: 'Meet-up and pick-up both available',
              desc: 'Customer can choose either one',
              children: null
            }
          ]
        },
        departure_reminder_list: {
          options: [
            {
              value: 1,
              label: 'Look for the guide with a Klook sign',
              desc: '',
              data: {
                time: 0
              }
            },
            {
              value: 2,
              label: "Latecomers or no-shows can't be refunded",
              desc: '',
              data: {
                time: 30
              }
            },
            {
              value: 3,
              label: 'See the package details for pick-up fees',
              desc: '',
              data: {
                time: 30
              }
            },
            {
              value: 4,
              label: 'This is a shared transfer and pick up could be early or late',
              desc: '',
              data: {
                time: 30
              }
            },
            {
              value: 5,
              label: 'Enter your hotel name and address at the checkout page',
              desc: '',
              data: {
                time: 30
              }
            },
            {
              value: 6,
              label: 'The operator will re-confirm your pick-up time in advance',
              desc: '',
              data: {
                time: 30
              }
            },
            {
              value: 7,
              label: 'Please arrive at the location {num} mins before the departure time',
              desc: '',
              data: {
                time: 45
              }
            }
          ]
        }
      },
      return: {
        return_type: {
          options: [
            {
              value: 0,
              label: 'No, tour ends at the last stop',
              desc: 'Customers will leave directly after the last stop',
              children: null
            },
            {
              value: 1,
              label: 'Yes, to drop-off point',
              desc: 'Transport provided to a drop-off point decided by the merchant',
              children: null
            },
            {
              value: 2,
              label: "Yes, to customers' accomodation",
              desc: "Transport provided to customers' hotel, airbnb etc",
              children: null
            }
          ]
        }
      },
      attraction: {
        admission_included_type: {
          options: [
            {
              value: 1,
              label: 'Required and included',
              desc: 'Ticket price is included in the tour',
              children: null
            },
            {
              value: 2,
              label: 'Required, but not included',
              desc: 'Customers must buy it either online or on site',
              children: null
            },
            {
              value: 3,
              label: 'Free entry',
              desc: 'No ticket is required at all',
              children: null
            },
            {
              value: 4,
              label: 'Required, and can be included or not included.',
              desc: "You'll be able to set different prices and inventory later on",
              children: null
            }
          ]
        },
        unique_feature: {
          options: [
            {
              value: 10001,
              label: 'Outdoor adventures',
              desc: 'Outdoor adventures',
              children: [
                {
                  value: 1,
                  label: 'Jungle swing',
                  desc: 'Jungle swing',
                  children: null
                },
                {
                  value: 2,
                  label: 'Sailing',
                  desc: 'Sailing',
                  children: null
                },
                {
                  value: 3,
                  label: 'Dune bashing',
                  desc: 'Dune bashing',
                  children: null
                },
                {
                  value: 4,
                  label: 'River tracing',
                  desc: 'River tracing',
                  children: null
                },
                {
                  value: 5,
                  label: 'Abseiling',
                  desc: 'Abseiling',
                  children: null
                },
                {
                  value: 6,
                  label: 'Extreme sports',
                  desc: 'Extreme sports',
                  children: null
                },
                {
                  value: 7,
                  label: 'High-altitude trekking',
                  desc: 'High-altitude trekking',
                  children: null
                },
                {
                  value: 8,
                  label: 'Jeep safari',
                  desc: 'Jeep safari',
                  children: null
                },
                {
                  value: 9,
                  label: 'Hot air balloon',
                  desc: 'Hot air balloon',
                  children: null
                },
                {
                  value: 10,
                  label: 'Rock climbing',
                  desc: 'Rock climbing',
                  children: null
                },
                {
                  value: 11,
                  label: 'Quad biking',
                  desc: 'Quad biking',
                  children: null
                },
                {
                  value: 12,
                  label: 'Cycling',
                  desc: 'Cycling',
                  children: null
                },
                {
                  value: 13,
                  label: 'Picnic',
                  desc: 'Picnic',
                  children: null
                },
                {
                  value: 14,
                  label: 'Sandboarding',
                  desc: 'Sandboarding',
                  children: null
                },
                {
                  value: 15,
                  label: 'Zipline experience',
                  desc: 'Zipline experience',
                  children: null
                },
                {
                  value: 16,
                  label: 'Cave exploration',
                  desc: 'Cave exploration',
                  children: null
                },
                {
                  value: 17,
                  label: 'Island hopping',
                  desc: 'Island hopping',
                  children: null
                },
                {
                  value: 18,
                  label: 'Bridge climbing',
                  desc: 'Bridge climbing',
                  children: null
                },
                {
                  value: 19,
                  label: 'Jetskiing',
                  desc: 'Jetskiing',
                  children: null
                },
                {
                  value: 20,
                  label: 'Helicopter riding',
                  desc: 'Helicopter riding',
                  children: null
                },
                {
                  value: 21,
                  label: 'ATV riding',
                  desc: 'ATV riding',
                  children: null
                },
                {
                  value: 22,
                  label: 'Diving',
                  desc: 'Diving',
                  children: null
                },
                {
                  value: 23,
                  label: 'Kayaking',
                  desc: 'Kayaking',
                  children: null
                },
                {
                  value: 24,
                  label: 'Snorkeling',
                  desc: 'Snorkeling',
                  children: null
                },
                {
                  value: 25,
                  label: 'Bouldering',
                  desc: 'Bouldering',
                  children: null
                },
                {
                  value: 26,
                  label: 'Hiking',
                  desc: 'Hiking',
                  children: null
                },
                {
                  value: 27,
                  label: 'Trekking',
                  desc: 'Trekking',
                  children: null
                },
                {
                  value: 28,
                  label: 'Ice climbing',
                  desc: 'Ice climbing',
                  children: null
                },
                {
                  value: 29,
                  label: 'Cliff diving',
                  desc: 'Cliff diving',
                  children: null
                },
                {
                  value: 30,
                  label: 'Catapult experience',
                  desc: 'Catapult experience',
                  children: null
                },
                {
                  value: 31,
                  label: 'Rail bike',
                  desc: 'Rail bike',
                  children: null
                },
                {
                  value: 32,
                  label: 'Heli hiking',
                  desc: 'Heli hiking',
                  children: null
                },
                {
                  value: 33,
                  label: 'Jet boat',
                  desc: 'Jet boat',
                  children: null
                },
                {
                  value: 34,
                  label: 'Sea walking',
                  desc: 'Sea walking',
                  children: null
                },
                {
                  value: 35,
                  label: 'Tubing',
                  desc: 'Tubing',
                  children: null
                },
                {
                  value: 36,
                  label: 'Parasailing',
                  desc: 'Parasailing',
                  children: null
                },
                {
                  value: 37,
                  label: 'Swimming',
                  desc: 'Swimming',
                  children: null
                },
                {
                  value: 38,
                  label: 'Banana boat',
                  desc: 'Banana boat',
                  children: null
                },
                {
                  value: 39,
                  label: 'Paragliding',
                  desc: 'Paragliding',
                  children: null
                },
                {
                  value: 40,
                  label: 'Skydiving',
                  desc: 'Skydiving',
                  children: null
                },
                {
                  value: 41,
                  label: 'Bungee jumping',
                  desc: 'Bungee jumping',
                  children: null
                }
              ]
            },
            {
              value: 10002,
              label: 'Cultural experiences',
              desc: 'Cultural experiences',
              children: [
                {
                  value: 42,
                  label: 'Pottery',
                  desc: 'Pottery',
                  children: null
                },
                {
                  value: 43,
                  label: 'Workshop and class',
                  desc: 'Workshop and class',
                  children: null
                },
                {
                  value: 44,
                  label: 'Qipao experience',
                  desc: 'Qipao experience',
                  children: null
                },
                {
                  value: 45,
                  label: 'Hanbok experience',
                  desc: 'Hanbok experience',
                  children: null
                },
                {
                  value: 46,
                  label: 'Kimono experience',
                  desc: 'Kimono experience',
                  children: null
                },
                {
                  value: 47,
                  label: 'Cosplay',
                  desc: 'Cosplay',
                  children: null
                },
                {
                  value: 48,
                  label: 'Traditional costume experience',
                  desc: 'Traditional costume experience',
                  children: null
                },
                {
                  value: 49,
                  label: 'Shopping',
                  desc: 'Shopping',
                  children: null
                },
                {
                  value: 50,
                  label: 'Tea making',
                  desc: 'Tea making',
                  children: null
                },
                {
                  value: 51,
                  label: 'Gold panning',
                  desc: 'Gold panning',
                  children: null
                },
                {
                  value: 52,
                  label: 'Fruit picking',
                  desc: 'Fruit picking',
                  children: null
                },
                {
                  value: 53,
                  label: 'Live performance',
                  desc: 'Live performance',
                  children: null
                }
              ]
            },
            {
              value: 10003,
              label: 'Wildlife and nature',
              desc: 'Wildlife and nature',
              children: [
                {
                  value: 54,
                  label: 'Dolphin watching',
                  desc: 'Dolphin watching',
                  children: null
                },
                {
                  value: 55,
                  label: 'Waterfall hike',
                  desc: 'Waterfall hike',
                  children: null
                },
                {
                  value: 56,
                  label: 'Elephant feeding',
                  desc: 'Elephant feeding',
                  children: null
                },
                {
                  value: 57,
                  label: 'Safari',
                  desc: 'Safari',
                  children: null
                },
                {
                  value: 58,
                  label: 'Sunset party',
                  desc: 'Sunset party',
                  children: null
                },
                {
                  value: 59,
                  label: 'Sunrise watching',
                  desc: 'Sunrise watching',
                  children: null
                },
                {
                  value: 60,
                  label: 'Whale watching',
                  desc: 'Whale watching',
                  children: null
                },
                {
                  value: 61,
                  label: 'Animal encounters',
                  desc: 'Animal encounters',
                  children: null
                },
                {
                  value: 62,
                  label: 'Animal feeding',
                  desc: 'Animal feeding',
                  children: null
                },
                {
                  value: 63,
                  label: 'Fishing',
                  desc: 'Fishing',
                  children: null
                },
                {
                  value: 64,
                  label: 'Stargazing',
                  desc: 'Stargazing',
                  children: null
                },
                {
                  value: 65,
                  label: 'Horse riding',
                  desc: 'Horse riding',
                  children: null
                },
                {
                  value: 66,
                  label: 'Canopy walk',
                  desc: 'Canopy walk',
                  children: null
                },
                {
                  value: 67,
                  label: 'Camel riding',
                  desc: 'Camel riding',
                  children: null
                },
                {
                  value: 68,
                  label: 'Night fishing',
                  desc: 'Night fishing',
                  children: null
                },
                {
                  value: 200,
                  label: 'Tulip fields',
                  desc: 'Tulip',
                  children: null
                },
                {
                  value: 201,
                  label: 'Lavender fields',
                  desc: 'Lavender',
                  children: null
                },
                {
                  value: 202,
                  label: 'Cherry blossoms',
                  desc: 'Cherry Blossom',
                  children: null
                },
                {
                  value: 203,
                  label: 'Firefly watching',
                  desc: 'Fireflies',
                  children: null
                },
                {
                  value: 204,
                  label: 'Maple leaf',
                  desc: 'Maple Leaf',
                  children: null
                },
                {
                  value: 205,
                  label: 'Northern lights',
                  desc: 'Northern Lights',
                  children: null
                }
              ]
            },
            {
              value: 10004,
              label: 'Food and drinks',
              desc: 'Food and drinks',
              children: [
                {
                  value: 69,
                  label: 'Local food tasting',
                  desc: 'Local food tasting',
                  children: null
                },
                {
                  value: 70,
                  label: 'Dining',
                  desc: 'Dining',
                  children: null
                },
                {
                  value: 71,
                  label: 'Street food tasting',
                  desc: 'Street food tasting',
                  children: null
                },
                {
                  value: 72,
                  label: 'Whiskey tasting',
                  desc: 'Whiskey tasting',
                  children: null
                },
                {
                  value: 73,
                  label: 'Matcha tasting',
                  desc: 'Matcha tasting',
                  children: null
                },
                {
                  value: 74,
                  label: 'Beer tasting',
                  desc: 'Beer tasting',
                  children: null
                },
                {
                  value: 75,
                  label: 'Chocolate tasting',
                  desc: 'Chocolate tasting',
                  children: null
                },
                {
                  value: 76,
                  label: 'Cheese tasting',
                  desc: 'Cheese tasting',
                  children: null
                },
                {
                  value: 77,
                  label: 'Wine tasting',
                  desc: 'Wine tasting',
                  children: null
                },
                {
                  value: 78,
                  label: 'Virtual dining',
                  desc: 'Virtual dining',
                  children: null
                }
              ]
            },
            {
              value: 10005,
              label: 'Wellness and relaxation',
              desc: 'Wellness and relaxation',
              children: [
                {
                  value: 79,
                  label: 'Wellness experience',
                  desc: 'Wellness experience',
                  children: null
                },
                {
                  value: 80,
                  label: 'Mud bath',
                  desc: 'Mud bath',
                  children: null
                },
                {
                  value: 81,
                  label: 'Yoga',
                  desc: 'Yoga',
                  children: null
                },
                {
                  value: 82,
                  label: 'Standup paddleboarding',
                  desc: 'Standup paddleboarding',
                  children: null
                },
                {
                  value: 83,
                  label: 'Mineral bath',
                  desc: 'Mineral bath',
                  children: null
                },
                {
                  value: 84,
                  label: 'Mineral therapy',
                  desc: 'Mineral therapy',
                  children: null
                },
                {
                  value: 85,
                  label: 'Elephant massage',
                  desc: 'Elephant massage',
                  children: null
                },
                {
                  value: 86,
                  label: 'Fish pedicure',
                  desc: 'Fish pedicure',
                  children: null
                },
                {
                  value: 206,
                  label: 'Meditation',
                  desc: 'Meditation',
                  children: null
                }
              ]
            },
            {
              value: 10006,
              label: 'Entertainment and nightlife',
              desc: 'Entertainment and nightlife',
              children: [
                {
                  value: 87,
                  label: 'Karaoke',
                  desc: 'Karaoke',
                  children: null
                },
                {
                  value: 88,
                  label: 'Escape room',
                  desc: 'Escape room',
                  children: null
                },
                {
                  value: 89,
                  label: 'Pub crawl',
                  desc: 'Pub crawl',
                  children: null
                },
                {
                  value: 90,
                  label: 'Scavenger hunt',
                  desc: 'Scavenger hunt',
                  children: null
                },
                {
                  value: 91,
                  label: 'Indoor games',
                  desc: 'Indoor games',
                  children: null
                },
                {
                  value: 92,
                  label: 'Shisha experience',
                  desc: 'Shisha experience',
                  children: null
                },
                {
                  value: 93,
                  label: 'Bar hopping',
                  desc: 'Bar hopping',
                  children: null
                }
              ]
            },
            {
              value: 10007,
              label: 'Sports and games',
              desc: 'Sports and games',
              children: [
                {
                  value: 94,
                  label: 'Golfing',
                  desc: 'Golfing',
                  children: null
                },
                {
                  value: 95,
                  label: 'Archery',
                  desc: 'Archery',
                  children: null
                },
                {
                  value: 96,
                  label: 'Surfing',
                  desc: 'Surfing',
                  children: null
                },
                {
                  value: 97,
                  label: 'Water sports',
                  desc: 'Water sports',
                  children: null
                },
                {
                  value: 98,
                  label: 'Driving',
                  desc: 'Driving',
                  children: null
                },
                {
                  value: 99,
                  label: 'Kart racing',
                  desc: 'Kart racing',
                  children: null
                },
                {
                  value: 100,
                  label: 'Skiing',
                  desc: 'Skiing',
                  children: null
                },
                {
                  value: 101,
                  label: 'Horse race watching',
                  desc: 'Horse race watching',
                  children: null
                },
                {
                  value: 102,
                  label: 'Sledding',
                  desc: 'Sledding',
                  children: null
                },
                {
                  value: 103,
                  label: 'Snow tubing',
                  desc: 'Snow tubing',
                  children: null
                },
                {
                  value: 104,
                  label: 'Beach volleyball',
                  desc: 'Beach volleyball',
                  children: null
                }
              ]
            },
            {
              value: 10008,
              label: 'Others',
              desc: 'Others',
              children: [
                {
                  value: 105,
                  label: 'For autumn',
                  desc: 'For Autumn',
                  children: null
                },
                {
                  value: 106,
                  label: 'For spring',
                  desc: 'For spring',
                  children: null
                },
                {
                  value: 107,
                  label: 'For summer',
                  desc: 'For summer',
                  children: null
                },
                {
                  value: 108,
                  label: 'For winter',
                  desc: 'For winter',
                  children: null
                },
                {
                  value: 210,
                  label: 'Instagrammable spot',
                  desc: 'Instagrammable spot',
                  children: null
                },
                {
                  value: 211,
                  label: 'Observatory deck',
                  desc: 'Observatory deck',
                  children: null
                }
              ]
            }
          ]
        }
      },
      transport: {
        vehicle_type: {
          options: [
            {
              value: 1,
              label: 'Car',
              desc: '',
              children: null
            },
            {
              value: 2,
              label: 'Bus',
              desc: '',
              children: null
            },
            {
              value: 3,
              label: 'Boat',
              desc: '',
              children: null
            },
            {
              value: 4,
              label: 'Train',
              desc: '',
              children: null
            },
            {
              value: 5,
              label: 'Riverboat',
              desc: '',
              children: null
            },
            {
              value: 6,
              label: 'Yacht',
              desc: '',
              children: null
            },
            {
              value: 7,
              label: 'On foot',
              desc: '',
              children: null
            },
            {
              value: 8,
              label: 'Motorbike',
              desc: '',
              children: null
            },
            {
              value: 9,
              label: 'Double-decker bus',
              desc: '',
              children: null
            },
            {
              value: 10,
              label: 'Coach',
              desc: '',
              children: null
            },
            {
              value: 11,
              label: 'Minibus',
              desc: '',
              children: null
            },
            {
              value: 12,
              label: 'Tuk-tuk',
              desc: '',
              children: null
            },
            {
              value: 13,
              label: 'Longtail boat',
              desc: '',
              children: null
            },
            {
              value: 14,
              label: 'Catamaran',
              desc: '',
              children: null
            },
            {
              value: 15,
              label: 'Speedboat',
              desc: '',
              children: null
            },
            {
              value: 16,
              label: 'Cruise',
              desc: '',
              children: null
            },
            {
              value: 17,
              label: 'Ferry',
              desc: '',
              children: null
            },
            {
              value: 18,
              label: 'Bullet train',
              desc: '',
              children: null
            },
            {
              value: 19,
              label: 'Small van',
              desc: '',
              children: null
            },
            {
              value: 20,
              label: 'Van',
              desc: '',
              children: null
            }
          ]
        }
      },
      meal: {
        meal_type: {
          options: [
            {
              value: 1,
              label: 'Breakfast',
              desc: '',
              children: null
            },
            {
              value: 2,
              label: 'Brunch',
              desc: '',
              children: null
            },
            {
              value: 3,
              label: 'Lunch',
              desc: '',
              children: null
            },
            {
              value: 4,
              label: 'Afternoon tea',
              desc: '',
              children: null
            },
            {
              value: 5,
              label: 'Dinner',
              desc: '',
              children: null
            },
            {
              value: 6,
              label: 'Snacks and drinks',
              desc: '',
              children: null
            },
            {
              value: 7,
              label: 'Drinks',
              desc: '',
              children: null
            },
            {
              value: 8,
              label: 'Snacks',
              desc: '',
              children: null
            },
            {
              value: 9,
              label: 'Late-night meal',
              desc: '',
              children: null
            }
          ]
        },
        cuisine_type: {
          options: [
            {
              value: 1,
              label: 'Barbecue',
              desc: 'JBarbecue',
              children: null
            },
            {
              value: 2,
              label: 'Fusion',
              desc: 'Fusion',
              children: null
            },
            {
              value: 3,
              label: 'Western',
              desc: 'Western',
              children: null
            },
            {
              value: 4,
              label: 'Asian',
              desc: 'Asian',
              children: null
            },
            {
              value: 5,
              label: 'Multiple cuisine options',
              desc: 'Multiple cuisine options',
              children: null
            },
            {
              value: 6,
              label: 'Buffet',
              desc: 'Buffet',
              children: null
            },
            {
              value: 7,
              label: 'Vietnamese',
              desc: 'Vietnamese',
              children: null
            },
            {
              value: 8,
              label: 'Vegetarian options',
              desc: 'Vegetarian options',
              children: null
            },
            {
              value: 9,
              label: 'Vegan options',
              desc: 'Vegan options',
              children: null
            },
            {
              value: 10,
              label: 'Caribbean',
              desc: 'Caribbean',
              children: null
            },
            {
              value: 11,
              label: 'Moroccan',
              desc: 'Moroccan',
              children: null
            },
            {
              value: 12,
              label: 'Ethiopian',
              desc: 'Ethiopian',
              children: null
            },
            {
              value: 13,
              label: 'Peruvian',
              desc: 'Peruvian',
              children: null
            },
            {
              value: 14,
              label: 'Brazilian',
              desc: 'Brazilian',
              children: null
            },
            {
              value: 15,
              label: 'Mediterranean',
              desc: 'Mediterranean',
              children: null
            },
            {
              value: 16,
              label: 'Lebanese',
              desc: 'Lebanese',
              children: null
            },
            {
              value: 17,
              label: 'Turkish',
              desc: 'Turkish',
              children: null
            },
            {
              value: 18,
              label: 'American',
              desc: 'American',
              children: null
            },
            {
              value: 19,
              label: 'Halal',
              desc: 'Halal',
              children: null
            },
            {
              value: 20,
              label: 'Middle Eastern',
              desc: 'Middle Eastern',
              children: null
            },
            {
              value: 21,
              label: 'Korean',
              desc: 'Korean',
              children: null
            },
            {
              value: 22,
              label: 'Greek',
              desc: 'Greek',
              children: null
            },
            {
              value: 23,
              label: 'Spanish',
              desc: 'Spanish',
              children: null
            },
            {
              value: 24,
              label: 'French',
              desc: 'French',
              children: null
            },
            {
              value: 25,
              label: 'Thai',
              desc: 'Thai',
              children: null
            },
            {
              value: 26,
              label: 'Indian',
              desc: 'Indian',
              children: null
            },
            {
              value: 27,
              label: 'Mexican',
              desc: 'Mexican',
              children: null
            },
            {
              value: 28,
              label: 'Japanese',
              desc: 'Japanese',
              children: null
            },
            {
              value: 29,
              label: 'Chinese',
              desc: 'Chinese',
              children: null
            },
            {
              value: 30,
              label: 'Italian',
              desc: 'Italian',
              children: null
            }
          ]
        }
      },
      accommodation: {
        accommodation_type: {
          options: [
            {
              value: 1,
              label: 'Hotel',
              desc: '',
              children: null
            },
            {
              value: 2,
              label: 'Guesthouse',
              desc: '',
              children: null
            },
            {
              value: 3,
              label: 'Apartment',
              desc: '',
              children: null
            },
            {
              value: 4,
              label: 'Hostel',
              desc: '',
              children: null
            },
            {
              value: 5,
              label: 'Resort',
              desc: '',
              children: null
            },
            {
              value: 6,
              label: 'Capsule',
              desc: '',
              children: null
            }
          ]
        },
        bed_type: {
          options: [
            {
              value: 1,
              label: '1 single bed',
              desc: '',
              children: null
            },
            {
              value: 2,
              label: '2 single beds',
              desc: '',
              children: null
            },
            {
              value: 3,
              label: '3 single beds',
              desc: '',
              children: null
            },
            {
              value: 4,
              label: 'Queen-size bed (for 2 people)',
              desc: '',
              children: null
            }
          ]
        },
        quantity_limitation: {
          options: [
            {
              value: 1,
              label: '1',
              desc: '',
              children: null
            },
            {
              value: 2,
              label: '2',
              desc: '',
              children: null
            },
            {
              value: 3,
              label: '3',
              desc: '',
              children: null
            },
            {
              value: 4,
              label: '4',
              desc: '',
              children: null
            },
            {
              value: 5,
              label: '5',
              desc: '',
              children: null
            },
            {
              value: 6,
              label: '6',
              desc: '',
              children: null
            },
            {
              value: 7,
              label: '7',
              desc: '',
              children: null
            },
            {
              value: 8,
              label: '8',
              desc: '',
              children: null
            },
            {
              value: 9,
              label: '9',
              desc: '',
              children: null
            },
            {
              value: 10,
              label: '10',
              desc: '',
              children: null
            }
          ]
        },
        property_rating: {
          options: [
            {
              value: 2,
              label: '2 stars or below (Economy)',
              desc: '',
              children: null
            },
            {
              value: 3,
              label: '3 stars (Standard)',
              desc: '',
              children: null
            },
            {
              value: 4,
              label: '4 stars (Premium)',
              desc: '',
              children: null
            },
            {
              value: 5,
              label: '5 stars (Luxury)',
              desc: '',
              children: null
            }
          ]
        },
        bathroom_facilities: {
          options: [
            {
              value: 1,
              label: 'Private bathroom',
              desc: 'Shared bathroom with other guests',
              children: null
            },
            {
              value: 2,
              label: 'Shared bathroom',
              desc: 'Private bathroom in the room',
              children: null
            }
          ]
        }
      }
    },
    audios: {},
    activity_departure_city: {
      area_id: 23301,
      area_name: 'Shenzhen City',
      node_type: 3,
      location: '22.543096,114.057865',
      country_area_id: 1020
    },
    merchant_currency: 'TWD',
    stock_out_type: 'KLOOKCODE',
    audio_permission: 1,
    language: 'en_US'
  }
}

export const getUnitType = {
  success: true,
  error: {
    code: '',
    message: ''
  },
  result: {
    spu_id: 99605,
    unit_type_list: [
      {
        unit_type: 1,
        unit_type_id: 1,
        local_name: '儿童1',
        en_name: 'Child',
        binded_type: 'ALL',
        use_status: 0,
        sort: 1,
        priority: 0
      },
      {
        unit_type: 2,
        unit_type_id: 2,
        local_name: 'Infant',
        en_name: 'Infant',
        binded_type: 'ALL',
        use_status: 0,
        sort: 2,
        priority: 0
      },
      {
        unit_type: 3,
        unit_type_id: 3,
        local_name: 'Youth',
        en_name: 'Youth',
        binded_type: 'ALL',
        use_status: 0,
        sort: 3,
        priority: 0
      },
      {
        unit_type: 4,
        unit_type_id: 4,
        local_name: 'Senior',
        en_name: 'Senior',
        binded_type: 'ALL',
        use_status: 0,
        sort: 4,
        priority: 0
      },
      {
        unit_type: 5,
        unit_type_id: 5,
        local_name: 'Other',
        en_name: 'Other',
        binded_type: 'ALL',
        use_status: 0,
        sort: 5,
        priority: 0
      },
      {
        unit_type: 6,
        unit_type_id: 6,
        local_name: 'People',
        en_name: 'People',
        binded_type: 'ALL',
        use_status: 0,
        sort: 6,
        priority: 0
      },
      {
        unit_type: 7,
        unit_type_id: 7,
        local_name: 'Postage',
        en_name: 'Postage',
        binded_type: 'ALL',
        use_status: 0,
        sort: 7,
        priority: 0
      },
      {
        unit_type: 8,
        unit_type_id: 8,
        local_name: 'Each',
        en_name: 'Each',
        binded_type: 'ALL',
        use_status: 0,
        sort: 8,
        priority: 0
      },
      {
        unit_type: 9,
        unit_type_id: 9,
        local_name: 'SIM',
        en_name: 'SIM',
        binded_type: 'ALL',
        use_status: 0,
        sort: 9,
        priority: 0
      },
      {
        unit_type: 10,
        unit_type_id: 10,
        local_name: 'Per SIM',
        en_name: 'Per SIM',
        binded_type: 'ALL',
        use_status: 0,
        sort: 10,
        priority: 0
      }
    ],
    rel_list: []
  }
}

export const menusList = [
  {
    menu_id: 1,
    menu_key: 'product_info',
    menu_name: 'Product info',
    type: 1,
    extra_info: {
      viewable: true,
      clickable: true,
      required: true,
      page_type: 'standard'
    },
    menu_tree_list: [
      {
        menu_id: 3,
        menu_key: 'departure_return',
        menu_name: 'Departure Return',
        type: 3,
        extra_info: {
          viewable: true,
          clickable: true,
          required: true,
          page_type: 'standard'
        }
      },
      {
        menu_id: 3,
        menu_key: 'itinerary',
        menu_name: 'Itinerary',
        type: 3,
        extra_info: {
          viewable: true,
          clickable: true,
          required: true,
          page_type: 'standard'
        }
      },
      {
        menu_id: 10,
        menu_key: 'other_info',
        menu_name: 'Other Info',
        type: 10,
        extra_info: {
          viewable: true,
          clickable: true,
          required: true,
          page_type: 'standard'
        }
      },
      {
        menu_id: 11,
        menu_key: 'operation_info',
        menu_name: 'Operation Info',
        type: 11,
        extra_info: {
          viewable: true,
          clickable: true,
          required: true,
          page_type: 'standard'
        }
      }
    ]
  },
  {
    menu_id: 2,
    menu_key: 'price_inventory',
    menu_name: 'Price & Inventory',
    type: 2,
    clickable: 0,
    menu_tree_list: [
      {
        menu_id: 20,
        menu_key: 'unit_type',
        menu_name: 'Unit Type',
        type: 20,
        extra_info: {
          viewable: true,
          clickable: true,
          required: true,
          page_type: 'standard'
        }
      },
      {
        menu_id: 22,
        menu_key: 'rules_settings',
        menu_name: 'Rules Settings',
        type: 22,
        clickable: 1,
        menu_list: []
      },
      {
        menu_id: 21,
        menu_key: 'inventory_schedule',
        menu_name: 'Inventory Schedule',
        type: 21,
        extra_info: {
          viewable: true,
          clickable: true,
          required: true,
          page_type: 'standard'
        },
        menu_tree_list: []
      }
    ]
  }
]
