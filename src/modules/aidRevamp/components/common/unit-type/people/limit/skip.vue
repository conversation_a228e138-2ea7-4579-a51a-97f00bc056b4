<template>
  <div class="skip-limit">
    <a-checkbox :checked="value" @change="$emit('input', $event.target.checked)">{{
      $t('196827')
    }}</a-checkbox>
  </div>
</template>

<script>
export default {
  name: 'skip-limit',
  components: {},
  props: {
    value: {
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {},
  methods: {}
}
</script>
<style lang="scss" scoped>
.ant-checkbox-wrapper {
  color: rgba(0, 0, 0, 0.85);
}
</style>
