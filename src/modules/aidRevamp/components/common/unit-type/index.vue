<template>
  <div class="unit-type common-form-item-reset-style">
    <div class="title-box common-flex-row-center">
      <span>{{ $t('package_unit_type') }}</span>
      <GuidanceVideo section-key="spu_menu_unit_type" />
    </div>
    <a-form-model ref="unitTypeFormRef" :model="formData" :rules="formDataRules">
      <a-form-model-item prop="unitTypeList" required :label="$t('196823')">
        <template v-if="styleType === 'checkout-expand-type'">
          <CommonCardCheckboxGroup
            v-model="formData.unitTypeList"
            :options="calcOptionList"
            :itemWidth="'auto'"
            class="checkbox-small-style"
            :disabled="$root.superEditLock"
            @checkboxChange="itemChangeHandler"
          ></CommonCardCheckboxGroup>
        </template>
        <template v-else-if="styleType === 'expand-more-type'">
          <ExpandMoreType
            v-model="formData.unitTypeList"
            :showInfo="calcShowInfo"
            :options="calcOptionList"
            @checkboxChange="itemChangeHandler"
            @updateValue="updateValueHandler"
          ></ExpandMoreType>
        </template>
        <template v-else>
          <a-select
            v-model="formData.unitTypeList"
            mode="multiple"
            :placeholder="$t('27986')"
            @select="itemChangeHandler(1, $event)"
            @deselect="itemChangeHandler(0, $event)"
          >
            <a-select-option v-for="(item, i) in calcOptionList" :key="i" :value="item.value">{{
              item.label
            }}</a-select-option>
          </a-select>
        </template>
      </a-form-model-item>
      <div v-if="formData.unitTypeList.length" class="people-list-box common-form-item-reset-card-style">
        <a-form-model-item :label="$t('196824')">
          <PeopleList
            ref="peopleListRef"
            :list.sync="data.rel_list"
            :controls="calcUnitTypeControl"
          ></PeopleList>
        </a-form-model-item>
      </div>
    </a-form-model>
  </div>
</template>

<script>
import { checkValidateRefs } from '~src/modules/aidRevamp/utils/index.js'
import PeopleList from '~src/modules/aidRevamp/components/common/unit-type/people/list.vue'
import CommonCardCheckboxGroup from '~src/modules/aidRevamp/components/common/card-checkbox-group/index.vue'
import * as validatorsUtils from '~src/modules/aidRevamp/utils/validators.js'
import ExpandMoreType from '~src/modules/aidRevamp/components/common/unit-type/expand-more-type/index.vue'
import GuidanceVideo from '@aidRevamp/components/common/guidance-video/index.vue'

const getInitRelItem = () => {
  const obj = {
    unit_type: 0,
    unit_type_id: 0,
    name_text_id: 0,
    en_name: '',
    local_name: '',
    extra_info: {
      customised_name: '',
      age_restriction: {
        min_age_range: 0,
        max_age_range: 0
      },
      booking_restrict: {
        min_num: 0,
        max_num: 9999
      },
      not_ask_unit_lv_other_info: false,
      required: 0
    }
  }
  return obj
}

export default {
  name: 'unit-type',
  components: {
    ExpandMoreType,
    CommonCardCheckboxGroup,
    PeopleList,
    GuidanceVideo
  },
  props: {
    data: {
      type: Object
    },
    styleType: {
      type: String,
      default: 'checkout-expand-type' // v2: expand-more-type | default: checkout-expand-type | 暂无场景使用：select-type
      // default: 'checkout-expand-type' // checkout-expand-type | select-type
    }
  },
  watch: {
    'data.rel_list': {
      immediate: true,
      deep: true,
      handler(arr) {
        if (!Array.isArray(arr)) {
          this.$set(this.data, 'rel_list', []) // 兼容后端返回null
          return
        }
        this.formData.unitTypeList = (arr || []).map((o) => o.unit_type_id)
      }
    }
  },
  data() {
    return {
      formDataRules: {
        unitTypeList: validatorsUtils.unitTypeRelListRules
      },
      formData: {
        unitTypeList: []
      }
    }
  },
  computed: {
    calcShowInfo() {
      return this.data?.show_info
    },
    calcUnitTypeControl() {
      return this.calcOptionList.reduce((acc, curr) => {
        if (curr.control) {
          acc[curr.unit_type_id] = curr.control
        }

        return acc
      }, {})
    },
    calcOptionList() {
      const arr = this.data?.unit_type_list?.map((o) => {
        return {
          ...o,
          label: o.local_name,
          value: o.unit_type_id
        }
      })
      return arr
    }
  },
  methods: {
    async validate() {
      const isb = await checkValidateRefs(['unitTypeFormRef', 'peopleListRef'], this)
      const flag = await this.$refs.peopleListRef?.validateFields()

      return isb && flag
    },
    itemChangeHandler(isSelect, value) {
      const { data } = this
      if (isSelect) {
        // 选中
        const index = data.rel_list?.findIndex((o) => o.unit_type_id === value)
        if (index !== -1) {
          return
        }
        const { unit_type, unit_type_id, en_name, local_name, name_text_id } = _.cloneDeep(
          this.calcOptionList.find((o) => o.unit_type_id === value) || {}
        )
        const initItem = {
          ...getInitRelItem(),
          ...{ unit_type, unit_type_id, en_name, local_name, name_text_id }
        }
        data.rel_list.push(initItem)
      } else {
        // 删除
        const index = data.rel_list?.findIndex((o) => o.unit_type_id === value)
        if (index !== -1) {
          data.rel_list.splice(index, 1)
        }
      }
    },
    updateValueHandler() {
      this.validate()
    },
    async saveUnitTypeApi(data) {
      const result = await ajax.post({
        url: ADMIN_API.aidRevamp.saveUnitType,
        data
      })
      return result
    }
  }
}
</script>
<style lang="scss" scoped>
.unit-type {
  max-width: 875px;
}
.people-list-box {
  margin-top: 24px;
}
.title-box {
  margin-bottom: 24px;
  font-size: 28px;
  font-weight: 600;
  line-height: 40px;
  color: rgba(0, 0, 0, 0.85);
}
.checkbox-small-style ::v-deep {
  .card-checkbox {
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
  }
}
.people-list-box.common-form-item-reset-card-style ::v-deep {
  .ant-form {
    .ant-form-item {
      margin-bottom: 20px;
      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
  .ant-form-item-control {
    line-height: 24px;
  }
  .ant-form-item-label {
    color: rgba(0, 0, 0, 0.85);
    font-weight: 400;
    line-height: 24px;
    label {
      font-size: 14px;
    }
  }
}
</style>
