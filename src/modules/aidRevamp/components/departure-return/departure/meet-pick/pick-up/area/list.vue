<template>
  <div class="area-list">
    <CardIndexAction
      :list="list"
      class="card-index-action-style"
      :hideSingleStyle="true"
      @delete="deleteHandler"
      @click="$emit('click')"
    >
      <AreaComp
        slot-scope="{ item, index }"
        :data="item"
        :data-index="index"
        class="area-comp-style"
        v-on="$listeners"
      ></AreaComp>
      <a-button
        slot="add-btn"
        v-bind="calcCommonProps"
        icon="plus"
        block
        class="add-btn"
        @click="$emit('add')"
      >
        {{ $t('global_button_add') }}
      </a-button>
    </CardIndexAction>
  </div>
</template>

<script>
import AreaComp from '~src/modules/aidRevamp/components/departure-return/departure/meet-pick/pick-up/area/index.vue'
import CardIndexAction from '~src/modules/aidRevamp/components/common/card-index-action/index.vue'

export default {
  name: 'area-list',
  components: {
    CardIndexAction,
    AreaComp
  },
  props: {
    size: {
      default: 'large'
    },
    list: {
      type: Array
    }
  },
  data() {
    return {}
  },
  computed: {
    calcCommonProps() {
      const { size } = this
      return {
        size
      }
    }
  },
  methods: {
    deleteHandler(index, _item) {
      this.list.splice(index, 1)
    }
  }
}
</script>
<style lang="scss" scoped>
.area-list {
}
.area-comp-style {
  background: rgba(246, 250, 254, 1);
}
.card-index-action-style {
  cursor: pointer;
}
</style>
