// 导入 main.vue 组件
import UnsavePriceReasonModal from './main.vue'
import Vue from 'vue'
import { UNSAFE_PRICE_CODE } from './constant'

function showUnsavePriceReasonModal({ warningMessage }) {
  return new Promise((resolve, reject) => {
    const UnsavePriceReasonModalConstructor = Vue.extend(UnsavePriceReasonModal)
    const instance = new UnsavePriceReasonModalConstructor({
      el: document.createElement('div'),
      propsData: {
        visible: true,
        warningMessage
      }
    })

    instance.$on('confirm', (payload) => {
      resolve({
        confirm: {
          ...payload
        },
        cancel: false
      })
      instance.$destroy()
      if (instance.$el.parentNode) {
        instance.$el.parentNode.removeChild(instance.$el)
      }
    })

    instance.$on('cancel', () => {
      resolve({
        confirm: false,
        cancel: true
      })
      instance.$destroy()
      if (instance.$el.parentNode) {
        instance.$el.parentNode.removeChild(instance.$el)
      }
    })

    document.body.appendChild(instance.$el)
  })
}

export { UNSAFE_PRICE_CODE, showUnsavePriceReasonModal }
