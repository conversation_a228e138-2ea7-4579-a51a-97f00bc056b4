import { message } from 'ant-design-vue'
import { UNSAFE_PRICE_CODE, showUnsavePriceReasonModal } from './index.js'

// 文档：https://klook.larksuite.com/wiki/SJXxwz3bBiuTrTk3K3wuXlENsrf
export default function checkUnsafePriceApisFactory() {
  let unsavePriceReason = {
    unsafe_price_reason_code: undefined,
    unsafe_price_reason_note: undefined
  }

  function getUnsafeParams() {
    // 如果存在不合格价格理由，将其添加到请求中
    return unsavePriceReason?.unsafe_price_reason_note ? { ...unsavePriceReason } : {}
  }

  async function checkUnsafePrice(res) {
    if (res.error?.code === UNSAFE_PRICE_CODE) {
      // 如果价格校验不通过，唤起 UnSavePriceReasonModal
      const { confirm, cancel } = await showUnsavePriceReasonModal({
        warningMessage: res.error.message
      })
      if (!cancel) {
        const { reasonCode, reasonNote } = confirm
        unsavePriceReason = {
          // 清空之前的理由
          unsafe_price_reason_code: reasonCode,
          unsafe_price_reason_note: reasonNote
        }
        return true
      }
    }

    unsavePriceReason = {
      unsafe_price_reason_code: undefined,
      unsafe_price_reason_note: undefined
    }
    return false
  }

  async function create_or_update_multi_schedule(data, options = {}) {
    const unsafeParams = getUnsafeParams()

    data = {
      ...data,
      ...unsafeParams
    }

    const res = await ajax.postBody(
      ADMIN_API.act.create_or_update_multi_schedule,
      {
        data,
        noDefaultResponseInterceptor: true
      },
      {
        ...options,
        msgOpt: {
          isErrMsg: false
        }
      }
    )

    const shouldRetry = await checkUnsafePrice(res)
    if (shouldRetry) {
      const retryRes = await create_or_update_multi_schedule(data)
      return retryRes
    }

    // 默认报错
    if (!res?.success && res?.error?.message && options?.msgOpt?.isErrMsg !== false) {
      message.error(res.error.message)
    }

    return res
  }

  async function creates_or_update_single_schedule_audit(data, options = {}) {
    const unsafeParams = getUnsafeParams()

    if (data.price) {
      data.price.unsafe_price_reason_note = unsafeParams.unsafe_price_reason_note
      data.price.unsafe_price_reason_code = unsafeParams.unsafe_price_reason_code
    }

    const res = await ajax.postBody(
      ADMIN_API.act.creates_or_update_single_schedule_audit,
      {
        data,
        noDefaultResponseInterceptor: true
      },
      {
        ...options,
        msgOpt: {
          isErrMsg: false
        }
      }
    )

    const shouldRetry = await checkUnsafePrice(res)
    if (shouldRetry) {
      const retryRes = await creates_or_update_single_schedule_audit(data)
      return retryRes
    }

    // 默认报错
    if (!res?.success && res?.error?.message && options?.msgOpt?.isErrMsg !== false) {
      message.error(res.error.message)
    }

    return res
  }

  async function creates_or_update_single_schedule(data, options = {}) {
    const unsafeParams = getUnsafeParams()

    if (data.price) {
      data.price.unsafe_price_reason_note = unsafeParams.unsafe_price_reason_note
      data.price.unsafe_price_reason_code = unsafeParams.unsafe_price_reason_code
    }

    const res = await ajax.postBody(
      ADMIN_API.act.creates_or_update_single_schedule,
      {
        data,
        noDefaultResponseInterceptor: true
      },
      {
        ...options,
        msgOpt: {
          isErrMsg: false
        }
      }
    )

    const shouldRetry = await checkUnsafePrice(res)
    if (shouldRetry) {
      const retryRes = await creates_or_update_single_schedule(data)
      return retryRes
    }

    // 默认报错
    if (!res?.success && res?.error?.message && options?.msgOpt?.isErrMsg !== false) {
      message.error(res.error.message)
    }

    return res
  }

  async function update_calendar_setting_audit(data, options = {}) {
    const unsafeParams = getUnsafeParams()

    if (data.fix_price) {
      data.fix_price.unsafe_price_reason_note = unsafeParams.unsafe_price_reason_note
      data.fix_price.unsafe_price_reason_code = unsafeParams.unsafe_price_reason_code
    }

    const res = await ajax.postBody(
      ADMIN_API.act.update_calendar_setting_audit,
      {
        data,
        noDefaultResponseInterceptor: true
      },
      {
        ...options,
        msgOpt: {
          isErrMsg: false
        }
      }
    )

    const shouldRetry = await checkUnsafePrice(res)
    if (shouldRetry) {
      const retryRes = await update_calendar_setting_audit(data)
      return retryRes
    }

    // 默认报错
    if (!res?.success && res?.error?.message && options?.msgOpt?.isErrMsg !== false) {
      message.error(res.error.message)
    }

    return res
  }

  async function update_calendar_setting(data, options = {}, isBatch = false) {
    const unsafeParams = getUnsafeParams()

    if (data.fix_price) {
      data.fix_price.unsafe_price_reason_note = unsafeParams.unsafe_price_reason_note
      data.fix_price.unsafe_price_reason_code = unsafeParams.unsafe_price_reason_code
    }

    const url = ADMIN_API.act[isBatch ? 'batch_update_calendar_setting' : 'update_calendar_setting']
    const res = await ajax.postBody(
      url,
      {
        data,
        noDefaultResponseInterceptor: true
      },
      {
        ...options,
        msgOpt: {
          isErrMsg: false
        }
      }
    )

    const shouldRetry = await checkUnsafePrice(res)
    if (shouldRetry) {
      const retryRes = await update_calendar_setting(data, options, isBatch)
      return retryRes
    }

    // 默认报错
    if (!res?.success && res?.error?.message && options?.msgOpt?.isErrMsg !== false) {
      message.error(res.error.message)
    }

    return res
  }

  return {
    create_or_update_multi_schedule,
    creates_or_update_single_schedule_audit,
    creates_or_update_single_schedule,
    update_calendar_setting_audit,
    update_calendar_setting
  }
}
