import API from '~src/modules/aidRevamp/api/url'

export function createUnitType(data) {
  return ajax.post({
    url: API.saveUnitType,
    data
  })
}

export function updateUnitType(data) {
  return ajax.post({
    url: API.updateUnitType,
    data
  })
}

export function getUnitTypeDetail(params) {
  return ajax.get({
    url: API.getUnitTypeDetail,
    params
  })
}
export function updateUnitTypeByState(data) {
  return ajax.post({
    url: API.updateUnitTypeByState,
    data
  })
}

export function getUnitType(data) {
  return ajax.post({
    url: API.getUnitList,
    data
  })
}

export function getVariantDetail(params, isAttraction) {
  return ajax.get({
    url: isAttraction ? API.getAttractionVariantDetail : API.getVariantDetail,
    params
  })
}

export function saveVariant(data, isAttraction) {
  return ajax.post({
    url: isAttraction ? API.saveAttractionVariant : API.saveVariant,
    data
  })
}

export function getVariantList(data, isAttraction) {
  return ajax.post({
    url: isAttraction ? API.getAttractionVariantList : API.getVariantList,
    data
  })
}

export function updateVariant(data, isAttraction) {
  return ajax.post({
    url: isAttraction ? API.updateAttractionVariant : API.updateVariant,
    data
  })
}

export function updatevariantByState(data, isAttraction) {
  return ajax.post({
    url: isAttraction ? API.updateAttractionVariantByState : API.updatevariantByState,
    data
  })
}

export async function saveProduct(data) {
  return await ajax.postBody({
    url: API.saveProduct,
    data
  })
}

export function createVariantValue(data, isAttraction) {
  return ajax.post({
    url: isAttraction ? API.create_Attractionvariant_value : API.create_variant_value,
    data
  })
}

export function updateVariantValue(data) {
  return ajax.post({
    url: API.update_variant_value,
    data
  })
}

export function updateVariantPriority(data) {
  return ajax.post({
    url: API.update_variant_value_rel_priority,
    data
  })
}

export function deleteVariantValue(data) {
  return ajax.post({
    url: API.delete_variant_value,
    data
  })
}

export function submitFeedback(data) {
  return ajax.post({
    url: API.submitFeedback,
    data
  })
}
