import API from '~src/modules/aidRevamp/api/url'

export function applicationList(data) {
  return ajax.post({
    url: API.get_attraction_list,
    data
  })
}

export function getSpuList(data) {
  return ajax.post({
    url: API.getSpuList,
    data
  })
}

export function updateSpuStatus(data) {
  return ajax.post({
    url: API.updateSpuStatus,
    data
  })
}

export function getUnitType(params) {
  return ajax.get({
    url: API.getUnitType,
    params
  })
}

export function saveUnitTypeBySpuId(data) {
  return ajax.post({
    url: API.saveUnitTypeBySpuId,
    data
  })
}

export function delUnitType(data) {
  return ajax.post({
    url: API.delUnitType,
    data
  })
}

export function deSelectUnitType(data) {
  return ajax.post({
    url: API.delUnitType,
    data
  })
}

export function saveUnitType(data) {
  return ajax.post({
    url: API.saveUnitType,
    data
  })
}

export function searchAttractionByName(data) {
  return ajax.post({
    url: API.search_attraction_by_name,
    data
  })
}

export function updateUnitTypeName(data) {
  return ajax.post({
    url: API.updateUnitTypeName,
    data
  })
}

export function saveAllUnitType(data) {
  return ajax.postBody({
    url: API.saveAllUnitType,
    data
  })
}

export function getSkuAndVariant(params) {
  return ajax.get({
    url: API.getSkuAndVariant,
    params
  })
}

export function getSkuList(params) {
  return ajax.get({
    url: API.getSkuList,
    params
  })
}

export function batchBindVariant(data) {
  return ajax.post({
    url: API.batchBindVariant,
    data
  })
}

export function createVariantValue(data) {
  return ajax.post({
    url: API.createVariantValue,
    data
  })
}

export function delVariantValue(data) {
  return ajax.post({
    url: API.delVariantValue,
    data
  })
}

export function variantConfigurationStatus(params) {
  return ajax.get({
    url: API.variantConfigurationStatus,
    params
  })
}

export function applyNewVariant(data) {
  return ajax.post({
    url: API.applyNewVariant,
    data
  })
}

export function createUnitTypeAndVariant(data) {
  return ajax.post({
    url: API.createOrUpdateSkusAndBindUnitTypeAndVariant,
    data
  })
}

export function updateBestPrice(data) {
  return ajax.post({
    url: API.updateBestPrice,
    data
  })
}

export function sortBindedVariant(data) {
  return ajax.post({
    url: API.sortBindedVariant,
    data
  })
}

export function publishedSkuBatch(data) {
  return ajax.post({
    url: API.publishedSkuBatch,
    data
  })
}

export function getLanguage(id) {
  return ajax.get({
    url: `/prosrv/activities/${id}/language`
  })
}

export function getLanguageAllStatu(id) {
  return ajax.get({
    url: `/prosrv/activities/${id}/status_all`
  })
}

export function getLanguageBlockStatus(params) {
  return ajax.get({
    url: '/prosrv/activities/language/block_status',
    params
  })
}

export function createSpu(data) {
  return ajax.post({
    url: API.createSpu,
    data
  })
}
