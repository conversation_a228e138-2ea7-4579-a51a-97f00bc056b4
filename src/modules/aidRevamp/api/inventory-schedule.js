import API from '~src/modules/aidRevamp/api/url'

export function getUnitType(params) {
  // console.log(params)
  return ajax.get({
    url: API.getUnitType,
    params
  })
}

export function getSkuAndVariant(params) {
  // console.log(params)
  return ajax.get({
    url: API.getSkuAndVariant,
    params
  })
}

export function batchBindVariant(data) {
  // console.log(data)
  return ajax.postBody({
    url: API.batchBindVariant,
    data
  })
}

export function createVariantValue(data) {
  // console.log(data)
  return ajax.postBody({
    url: API.createVariantValue,
    data
  })
}

export function delVariantValue(data) {
  // console.log(data)
  return ajax.postBody({
    url: API.delVariantValue,
    data
  })
}

export function getSkuList(params) {
  // console.log(params)
  return ajax.get({
    url: API.getSkuList,
    params
  })
}

export function saveBestPriceSku(data) {
  // console.log(data)
  return ajax.post({
    url: API.saveBestPriceSku,
    data
  })
}

export function createOrUpdateSkusAndBindUnitTypeAndVariant(data) {
  // console.log(data)
  return ajax.postBody({
    url: API.createOrUpdateSkusAndBindUnitTypeAndVariant,
    data
  })
}

export function getSkuModel(params) {
  // console.log(params)
  return ajax.get({
    url: API.getSkuModel,
    params
  })
}

export function createSkuModel(data) {
  // console.log(data)
  return ajax.postBody({
    url: API.createSkuModel,
    data
  })
}

export function createSkuModelV2(data) {
  // console.log(data)
  return ajax.postBody({
    url: API.createSkuModelV2,
    data
  })
}

export function updateSkuModel(data) {
  // console.log(data)
  return ajax.postBody({
    url: API.updateSkuModel,
    data
  })
}

export function copySkuSchedule(data) {
  // console.log(data)
  return ajax.postBody({
    url: API.copySkuSchedule,
    data
  })
}
